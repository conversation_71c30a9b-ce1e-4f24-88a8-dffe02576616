import Safe from '@safe-global/protocol-kit';
import { createSafeContext } from '@/utils/createSafeContext';

type SafeContextProps = {
  safeInstance: Safe | null;
  isLoading: boolean;
  signer?: {
    privateKey: string;
    seedPhrase: string;
  } | null;
  safeWallet: string | null;
};

export const [SafeContextProvider, useSafeContext] = createSafeContext<SafeContextProps>(
  'SafeContextProvider component was not found in tree'
);
