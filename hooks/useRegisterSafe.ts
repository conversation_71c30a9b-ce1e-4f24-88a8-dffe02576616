import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { Address, encodeFunctionData, Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { sponsorRequest } from '@/apis/sponsor';
import { env } from '@/utils/env';
import { getAuthorization } from '@/utils/web3';

type Payload = {
  safeAddress: Address;
  minKeysRequired: number;
  protocolKit: Safe;
  ownersPk: Hex[];
};

export const useRegisterSafe = () => {
  return useMutation({
    mutationFn: async ({ minKeysRequired, safeAddress, protocolKit, ownersPk }: Payload) => {
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const transactionData = encodeFunctionData({
        abi: GeoSafeAbi,
        functionName: 'registerSafe',
        args: [safeAddress, minKeysRequired],
      });

      let registerSafeTransaction = await protocolKit.createTransaction({
        transactions: [
          {
            to: env.GEOSAFE_CONTRACT_ADDRESS,
            data: transactionData,
            value: '0',
          },
        ],
      });

      const safeTxHash = await protocolKit.getTransactionHash(registerSafeTransaction);

      await Promise.all(
        ownersPk.map(async (pk) => {
          const kit = await Safe.init({
            provider: env.RPC_URL,
            safeAddress,
            signer: pk,
          });
          const signatureHash = await kit.signHash(safeTxHash);
          registerSafeTransaction.addSignature(signatureHash);
        })
      );

      const encodedTx = (await protocolKit.getEncodedTransaction(registerSafeTransaction)) as Hex;
      const serializedAuth = await getAuthorization(walletClient, env.GEOSAFE_CONTRACT_ADDRESS);

      const hash = await sponsorRequest({ txData: encodedTx, to: safeAddress, serializedAuth });
      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
  });
};
