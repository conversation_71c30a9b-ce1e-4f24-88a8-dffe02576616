import { useMutation } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { RECOVERY_KEYS_NEW_WALLET_STORE_KEY } from './useSaveRecoveryKeysNewWallet';
import { BIOMETRIC_STORE_KEY } from './useSetupBiometric';
import { PASS_CODE_STORE_KEY } from './useSetupPin';
import { RECOVERY_KEY_STORE_KEY } from './useSetupRecoveryKey';
import { PRIVATE_KEY_STORE_KEY, SEED_PHRASE_STORE_KEY } from './useSetupWallet';

export const useResetWallet = () => {
  return useMutation({
    mutationFn: async () => {
      await Promise.all([
        SecureStore.deleteItemAsync(PRIVATE_KEY_STORE_KEY),
        SecureStore.deleteItemAsync(SEED_PHRASE_STORE_KEY),
        SecureStore.deleteItemAsync(RECOVERY_KEY_STORE_KEY),
        SecureStore.deleteItemAsync(BIOMETRIC_STORE_KEY),
        SecureStore.deleteItemAsync(PASS_CODE_STORE_KEY),
        SecureStore.deleteItemAsync(RECOVERY_KEYS_NEW_WALLET_STORE_KEY),
      ]);

      return true;
    },
  });
};
