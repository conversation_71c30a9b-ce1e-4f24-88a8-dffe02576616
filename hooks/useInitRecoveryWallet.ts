import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { Address, encodeFunctionData, encodePacked, Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { sponsorRequest } from '@/apis/sponsor';
import { env } from '@/utils/env';
import { getAuthorization, getPublicClient } from '@/utils/web3';
import { DEFAULT_VALIDITY_BLOCKS, TRecoveryKey } from './useAddRecoveryKeys';

type Payload = {
  safeAddress: Address;
  recoveryKey: Hex;
  newKeys: TRecoveryKey[];
  protocolKit: Safe;
};

export const useInitRecoveryWallet = () => {
  return useMutation({
    mutationFn: async ({ recoveryKey, safeAddress, newKeys, protocolKit }: Payload) => {
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const publicClient = getPublicClient();
      const currentBlockNumber = await publicClient.getBlockNumber();
      const newKeysMap = newKeys.map((key) => ({
        ...key,
        validUntilBlock: currentBlockNumber + BigInt(DEFAULT_VALIDITY_BLOCKS),
      }));

      const serializedAuth = await getAuthorization(walletClient, env.GEOSAFE_CONTRACT_ADDRESS);

      const messHash = encodePacked(['bytes32'], [recoveryKey]);
      const signature = await walletClient.signMessage({
        account: walletClient.account,
        message: {
          raw: messHash,
        },
      });

      const txData = encodeFunctionData({
        abi: GeoSafeAbi,
        functionName: 'initiateRecovery',
        args: [safeAddress, recoveryKey, signature, newKeysMap],
      });

      const hash = await sponsorRequest({ txData, to: env.GEOSAFE_CONTRACT_ADDRESS, serializedAuth });
      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
  });
};
