import { useMutation } from '@tanstack/react-query';
import { Address } from 'viem';
import { requestApi } from '@/apis/requestApi';

type Payload = {
  safeAddress: Address;
  signerAddress: Address;
  fcmToken: string;
};

type Response = {
  id: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  isActive: boolean;
  safeId: number;
  signerId: number;
  fcmTokenId: number;
};

export const useRegisterPushNotification = () => {
  return useMutation({
    mutationFn: async ({ signerAddress, safeAddress, fcmToken }: Payload) => {
      const data = await requestApi<Response>({
        path: '/api/v1/user',
        method: 'POST',
        body: { signerAddress, safeAddress, fcmToken },
      });

      return data;
    },
  });
};
