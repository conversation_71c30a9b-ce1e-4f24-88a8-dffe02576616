import { TokenInfoResponse } from '@safe-global/api-kit';
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { isAddress, zeroAddress } from 'viem';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit, CURRENT_CHAIN } from '@/utils/web3';

export const useGetTokenInfo = (
  { tokenAddress }: { tokenAddress: string },
  queryOptions?: UseQueryOptions<TokenInfoResponse, Error, TokenInfoResponse>
) => {
  return useQuery({
    queryKey: queryKeys.tokenInfo.concat([tokenAddress!]),
    queryFn: async () => {
      if (tokenAddress === zeroAddress)
        return {
          symbol: CURRENT_CHAIN.nativeCurrency.symbol,
          decimals: CURRENT_CHAIN.nativeCurrency.decimals,
          name: CURRENT_CHAIN.nativeCurrency.name,
          type: 'N<PERSON><PERSON>',
          address: zeroAddress,
          trusted: true,
          logoUri: '',
        };

      const tokenInfo = await apiKit.getToken(tokenAddress);
      return tokenInfo;
    },
    enabled: !!tokenAddress && isAddress(tokenAddress),
    ...queryOptions,
  });
};
