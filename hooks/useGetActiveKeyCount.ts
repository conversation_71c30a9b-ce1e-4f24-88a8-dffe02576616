import { useMutation } from '@tanstack/react-query';
import { Address, PublicClient } from 'viem';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { getPublicClient } from '@/utils/web3';

type Payload = {
  safeAddress: Address;
};

export const useGetActiveKeyCount = () => {
  return useMutation({
    mutationFn: async ({ safeAddress }: Payload) => {
      const client: PublicClient = getPublicClient();
      const response = await client.readContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'getActiveKeyCount',
        args: [safeAddress],
      });

      return response;
    },
  });
};
