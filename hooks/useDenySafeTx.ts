import { useMutation, useQueryClient } from '@tanstack/react-query';
import { privateKeyToAddress } from 'viem/accounts';
import { useSafeContext } from '@/context/safeContext';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit } from '@/utils/web3';

export const useDenySafeTx = () => {
  const { safeWallet, safeInstance, signer } = useSafeContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ safeTxHash }: { safeTxHash: string }) => {
      if (!safeWallet) throw new Error('No safe wallet found');
      if (!safeInstance) throw new Error('No safe instance found');
      if (!signer) throw new Error('No signer found');
      const signerAddress = privateKeyToAddress(`0x${signer.privateKey}`);

      const safeTxMultisig = await apiKit.getTransaction(safeTxHash);
      const safeTx = await safeInstance.toSafeTransactionType(safeTxMultisig);
      const rejectionTx = await safeInstance.createRejectionTransaction(safeTx.data.nonce);
      const txHashReject = await safeInstance.getTransactionHash(rejectionTx);
      const signature = await safeInstance.signHash(txHashReject);

      await apiKit.proposeTransaction({
        safeAddress: safeWallet,
        safeTransactionData: rejectionTx.data,
        safeTxHash: txHashReject,
        senderAddress: signerAddress,
        senderSignature: signature.data,
      });
      return true;
    },
    onSettled(data, error) {
      if (!error) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.safeTxsPending,
        });
      }
    },
  });
};
