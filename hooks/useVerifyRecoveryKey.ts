import { useMutation } from '@tanstack/react-query';
import { Address, Hex, PublicClient } from 'viem';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { getPublicClient } from '@/utils/web3';

type Payload = {
  recoveryKey: Hex;
  safeAddress: Address;
};

export const useVerifyRecoveryKey = () => {
  return useMutation({
    mutationFn: async ({ recoveryKey, safeAddress }: Payload) => {
      const client: PublicClient = getPublicClient();
      const response = await client.readContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'canUseRecoveryKey',
        args: [safeAddress, recoveryKey],
      });

      return response;
    },
  });
};
