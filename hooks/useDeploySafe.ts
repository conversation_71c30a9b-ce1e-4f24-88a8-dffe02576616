import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { sponsorRequest } from '@/apis/sponsor';
import { getAuthorization } from '@/utils/web3';

type Payload = {
  protocolKit: Safe;
};

export const useDeploySafe = () => {
  return useMutation({
    mutationFn: async ({ protocolKit }: Payload) => {
      const safeDeploymentTransaction = await protocolKit.createSafeDeploymentTransaction();
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();

      if (!walletClient) throw new Error('No wallet client found');

      const serializedAuth = await getAuthorization(walletClient, safeDeploymentTransaction.to);
      const hash = await sponsorRequest({
        txData: safeDeploymentTransaction.data as Hex,
        to: safeDeploymentTransaction.to,
        serializedAuth,
      });

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
  });
};
