import { SafeMultisigTransactionListResponse } from '@safe-global/api-kit';
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query';
import { useSafeContext } from '@/context/safeContext';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit } from '@/utils/web3';

export const useGetSafeTxsPending = (
  { limit = 10 },
  queryOptions?: UseInfiniteQueryOptions<
    SafeMultisigTransactionListResponse,
    Error,
    InfiniteData<SafeMultisigTransactionListResponse>,
    any,
    number
  >
) => {
  const { safeWallet } = useSafeContext();

  return useInfiniteQuery({
    queryKey: queryKeys.safeTxsPending.concat([safeWallet!]),
    queryFn: async (pageParam) => {
      if (!safeWallet) throw new Error('No safe wallet found');

      const pendingTransactions = await apiKit.getPendingTransactions(safeWallet, {
        limit: limit,
        offset: pageParam.pageParam * limit,
      });
      return pendingTransactions;
    },
    getNextPageParam: (lastPage, allPages = [], lastPageParam) => {
      return lastPage?.next ? Number(lastPageParam + 1) : undefined;
    },
    initialPageParam: 0,
    enabled: !!safeWallet,
    staleTime: 30000,
    ...queryOptions,
  });
};
