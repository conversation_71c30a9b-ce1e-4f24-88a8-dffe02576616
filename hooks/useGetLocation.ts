import { useMutation } from '@tanstack/react-query';
import * as Location from 'expo-location';

export const useGetLocation = () => {
  return useMutation({
    mutationFn: async () => {
      let { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      let location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Highest,
      });

      return location;
    },
  });
};
