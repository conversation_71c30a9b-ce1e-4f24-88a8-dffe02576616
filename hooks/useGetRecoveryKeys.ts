import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { PublicClient, ReadContractReturnType } from 'viem';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { queryKeys } from '@/utils/queryKeys';
import { getPublicClient } from '@/utils/web3';

type Props = {
  safeAddress: string;
};

export const useGetRecoveryKeys = (
  { safeAddress }: Props,
  queryOptions: Partial<UseQueryOptions<ReadContractReturnType<typeof GeoSafeAbi, 'getRecoveryKeys', [string]>>>
) => {
  return useQuery({
    queryKey: queryKeys.recoveryKeys,
    queryFn: async () => {
      const client: PublicClient = getPublicClient();
      const response = await Promise.all([
        client.readContract({
          address: env.GEOSAFE_CONTRACT_ADDRESS,
          abi: GeoSafeAbi,
          functionName: 'getPendingRecoveryKeys',
          args: [safeAddress],
        }),
        client.readContract({
          address: env.GEOSAFE_CONTRACT_ADDRESS,
          abi: GeoSafeAbi,
          functionName: 'getAvailableRecoveryKeys',
          args: [safeAddress],
        }),
      ]);

      return response.flat();
    },
    ...queryOptions,
  });
};
