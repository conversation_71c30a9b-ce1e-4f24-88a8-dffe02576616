import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { useSafeContext } from '@/context/safeContext';
import { queryKeys } from '@/utils/queryKeys';

export const useSafeWalletInfo = (queryOptions?: Partial<UseQueryOptions<{ threshold: number; owners: string[] }>>) => {
  const { safeInstance, safeWallet } = useSafeContext();

  return useQuery({
    queryKey: queryKeys.safeWalletInfo,
    queryFn: async () => {
      if (!safeWallet) throw new Error('No safe wallet found');
      if (!safeInstance) throw new Error('No safe instance found');

      const [threshold, owners] = await Promise.all([safeInstance.getThreshold(), safeInstance.getOwners()]);

      return {
        threshold,
        owners,
      };
    },
    enabled: !!safeWallet && !!safeInstance,
    ...queryOptions,
  });
};
