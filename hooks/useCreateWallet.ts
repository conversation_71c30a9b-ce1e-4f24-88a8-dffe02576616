import { useMutation } from '@tanstack/react-query';
import { LocationObject } from 'expo-location';
import { encodeLocation, genEOAWallet, mnemonicToPK } from '@/utils/genWallet';

type Payload = {
  recoveryKey: string;
  location: LocationObject;
  stretchingCount?: number;
};

export type TWallet = {
  privateKey: string;
  seedPhrase: string;
  stretchingCount: number;
};

export const useCreateWallet = () => {
  return useMutation<TWallet, Error, Payload>({
    mutationFn: async ({ recoveryKey, location, stretchingCount = __DEV__ ? 10 : 100000 }: Payload) => {
      const locationHash = encodeLocation(location?.coords.latitude, location?.coords.longitude, 10);

      const wallet = await genEOAWallet({
        locationHash,
        recoveryKey,
        iterations: stretchingCount,
      });

      const privateKeyHex = mnemonicToPK(wallet.seedPhrase);

      return {
        privateKey: privateKeyHex,
        seedPhrase: wallet?.seedPhrase,
        stretchingCount,
      };
    },
  });
};
