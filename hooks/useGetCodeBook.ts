import { useMutation } from '@tanstack/react-query';
import { Hex, PublicClient } from 'viem';
import { GeoSafeAbi } from '@/abis/geosafe';
import { env } from '@/utils/env';
import { getPublicClient } from '@/utils/web3';

type Payload = {
  recoveryKey: Hex;
};

export const useGetCodeBook = () => {
  return useMutation({
    mutationFn: async ({ recoveryKey }: Payload) => {
      const client: PublicClient = getPublicClient();
      const response = await client.readContract({
        address: env.GEOSAFE_CONTRACT_ADDRESS,
        abi: GeoSafeAbi,
        functionName: 'recoveryKeys',
        args: [recoveryKey],
      });

      return response;
    },
  });
};
