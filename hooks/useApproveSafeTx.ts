import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useSafeContext } from '@/context/safeContext';
import { queryKeys } from '@/utils/queryKeys';
import { apiKit } from '@/utils/web3';

export const useApproveSafeTx = () => {
  const { safeWallet, safeInstance } = useSafeContext();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ safeTxHash }: { safeTxHash: string }) => {
      if (!safeWallet) throw new Error('No safe wallet found');
      if (!safeInstance) throw new Error('No safe instance found');

      const signature = await safeInstance.signHash(safeTxHash);
      const signatureResponse = await apiKit.confirmTransaction(safeTxHash, signature.data);
      return signatureResponse;
    },
    onSettled(data, error) {
      if (!error) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.safeTxsPending,
        });
      }
    },
  });
};
