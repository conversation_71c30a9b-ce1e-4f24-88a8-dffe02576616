import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { Address, encodeFunctionData, Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { sponsorRequest } from '@/apis/sponsor';
import { env } from '@/utils/env';
import { getAuthorization, getPublicClient } from '@/utils/web3';

export const DEFAULT_VALIDITY_BLOCKS = 6 * 30 * 24 * 60 * 4; // ~6 months

export enum RecoveryPurpose {
  ADD_SIGNER = 0, // Can only add new signer
  ADD_RECOVERY_KEYS = 1, // Can only add new recovery keys
  BOTH = 2, // Can do both operations
}

export type TRecoveryKey = {
  keyId: Hex;
  safe: Address;
  expectedAddress: Address;
  validUntilBlock: bigint;
  isUsed: boolean;
  hint: string;
  stretchedCount: number;
};

type Payload = {
  safeAddress: Address;
  keys: TRecoveryKey[];
  protocolKit: Safe;
  ownersPk: Hex[];
};

export const useAddRecoveryKeys = () => {
  return useMutation({
    mutationFn: async ({ keys, safeAddress, protocolKit, ownersPk }: Payload) => {
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const publicClient = getPublicClient();
      const currentBlockNumber = await publicClient.getBlockNumber();
      const keysMap = keys.map((key) => ({
        ...key,
        validUntilBlock: currentBlockNumber + BigInt(DEFAULT_VALIDITY_BLOCKS),
      }));

      const transactionData = encodeFunctionData({
        abi: GeoSafeAbi,
        functionName: 'addRecoveryKeys',
        args: [safeAddress, keysMap],
      });

      let addRecoveryKeysSafeTransaction = await protocolKit.createTransaction({
        transactions: [
          {
            to: env.GEOSAFE_CONTRACT_ADDRESS,
            data: transactionData,
            value: '0',
          },
        ],
      });

      const safeTxHash = await protocolKit.getTransactionHash(addRecoveryKeysSafeTransaction);

      await Promise.all(
        ownersPk.map(async (pk) => {
          const kit = await Safe.init({
            provider: env.RPC_URL,
            safeAddress,
            signer: pk,
          });
          const signatureHash = await kit.signHash(safeTxHash);
          addRecoveryKeysSafeTransaction.addSignature(signatureHash);
        })
      );

      const encodedTx = (await protocolKit.getEncodedTransaction(addRecoveryKeysSafeTransaction)) as Hex;

      const serializedAuth = await getAuthorization(walletClient, env.GEOSAFE_CONTRACT_ADDRESS);
      const hash = await sponsorRequest({ txData: encodedTx, to: safeAddress, serializedAuth });
      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
  });
};
