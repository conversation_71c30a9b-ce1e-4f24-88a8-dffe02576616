import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { encodeFunctionData, Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { GeoSafeAbi } from '@/abis/geosafe';
import { sponsorRequest } from '@/apis/sponsor';
import { env } from '@/utils/env';
import { getAuthorization } from '@/utils/web3';

type Payload = {
  recoveryId: Hex;
  protocolKit: Safe;
};

export const useExecuteRecoveryWallet = () => {
  return useMutation({
    mutationFn: async ({ recoveryId, protocolKit }: Payload) => {
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const serializedAuth = await getAuthorization(walletClient, env.GEOSAFE_CONTRACT_ADDRESS);

      const txData = encodeFunctionData({
        abi: GeoSafe<PERSON>bi,
        functionName: 'executeRecovery',
        args: [recoveryId],
      });

      const hash = await sponsorRequest({ txData, to: env.GEOSAFE_CONTRACT_ADDRESS, serializedAuth });
      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });

      return receipt;
    },
  });
};
