import SafeApiKit from '@safe-global/api-kit';
import { useMutation } from '@tanstack/react-query';
import { encodeFunctionData, erc20Abi, parseUnits, zeroAddress } from 'viem';
import { privateKeyToAddress } from 'viem/accounts';
import { useSafeContext } from '@/context/safeContext';
import { env } from '@/utils/env';
import { CURRENT_CHAIN } from '@/utils/web3';
import { TokenResult } from './useGetSafeBalances';

type Payload = {
  transfers: {
    recipientAddress: string;
    amount: string;
    tokenInfo?: TokenResult;
  }[];
};

export const useCreateTxTransferToken = () => {
  const { safeInstance, safeWallet, signer } = useSafeContext();

  return useMutation({
    mutationFn: async ({ transfers }: Payload) => {
      if (!safeWallet) throw new Error('No safe wallet found');

      if (!safeInstance) throw new Error('No safe instance found');
      if (!signer) throw new Error('No signer found');
      const signerAddress = privateKeyToAddress(`0x${signer.privateKey}`);

      let transactions;

      transactions = transfers.map((transfer) => {
        const { recipientAddress, amount, tokenInfo } = transfer;
        const tokenAddress = tokenInfo?.tokenAddress || zeroAddress;
        const decimal = tokenInfo?.token?.decimals || CURRENT_CHAIN.nativeCurrency.decimals;

        if (tokenAddress === zeroAddress) {
          return {
            to: recipientAddress,
            data: '0x',
            value: parseUnits(amount, decimal).toString(),
          } as const;
        }

        const transactionData = encodeFunctionData({
          abi: erc20Abi,
          functionName: 'transfer',
          args: [recipientAddress, parseUnits(amount, decimal)],
        });

        return {
          to: tokenAddress,
          data: transactionData,
          value: '0',
        } as const;
      });

      let safeTransaction = await safeInstance.createTransaction({
        transactions,
      });

      const apiKit = new SafeApiKit({
        chainId: BigInt(CURRENT_CHAIN.id),
        apiKey: env.SAFE_API_KEY,
      });

      // Deterministic hash based on transaction parameters
      const safeTxHash = await safeInstance.getTransactionHash(safeTransaction);

      // Sign transaction to verify that the transaction is coming from owner 1
      const senderSignature = await safeInstance.signHash(safeTxHash);

      await apiKit.proposeTransaction({
        safeAddress: safeWallet,
        safeTransactionData: safeTransaction.data,
        safeTxHash,
        senderAddress: signerAddress,
        senderSignature: senderSignature.data,
      });
    },
  });
};
