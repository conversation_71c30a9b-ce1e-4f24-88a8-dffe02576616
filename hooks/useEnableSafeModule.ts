import Safe from '@safe-global/protocol-kit';
import { useMutation } from '@tanstack/react-query';
import { Address, encodeFunctionData, Hex } from 'viem';
import { waitForTransactionReceipt } from 'viem/actions';
import { SafeAbi } from '@/abis/safe';
import { sponsorRequest } from '@/apis/sponsor';
import { env } from '@/utils/env';
import { getAuthorization } from '@/utils/web3';

type Payload = {
  safeAddress: Address;
  protocolKit: Safe;
  ownersPk: Hex[];
};

export const useEnableSafeModule = () => {
  return useMutation({
    mutationFn: async ({ safeAddress, protocolKit, ownersPk }: Payload) => {
      const walletClient = await protocolKit.getSafeProvider().getExternalSigner();
      if (!walletClient) throw new Error('No wallet client found');

      const transactionData = encodeFunctionData({
        abi: SafeAbi,
        functionName: 'enableModule',
        args: [env.GEOSAFE_CONTRACT_ADDRESS],
      });

      let safeEnableModuleTransaction = await protocolKit.createTransaction({
        transactions: [
          {
            to: safeAddress,
            data: transactionData,
            value: '0',
            operation: 0, // CALL
          },
        ],
      });

      const safeTxHash = await protocolKit.getTransactionHash(safeEnableModuleTransaction);

      await Promise.all(
        ownersPk.map(async (pk) => {
          const kit = await Safe.init({
            provider: env.RPC_URL,
            safeAddress,
            signer: pk,
          });
          const signatureHash = await kit.signHash(safeTxHash);
          safeEnableModuleTransaction.addSignature(signatureHash);
        })
      );

      const encodedTx = await protocolKit.getEncodedTransaction(safeEnableModuleTransaction);

      const serializedAuth = await getAuthorization(walletClient, safeAddress);
      const hash = await sponsorRequest({ txData: encodedTx as Hex, to: safeAddress, serializedAuth });

      if (!hash) throw new Error('No hash returned');

      const receipt = await waitForTransactionReceipt(walletClient, { hash, confirmations: 0 });
      return receipt;
    },
  });
};
