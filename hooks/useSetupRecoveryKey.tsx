import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as SecureStore from 'expo-secure-store';
import { queryKeys } from '@/utils/queryKeys';

export const RECOVERY_KEY_STORE_KEY = 'geoSafeRecoveryKey';

export const useSetupRecoveryKey = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (recoveryKey: string) => {
      await SecureStore.setItemAsync(RECOVERY_KEY_STORE_KEY, recoveryKey);

      return recoveryKey;
    },
    async onSuccess() {
      await queryClient.resetQueries({ queryKey: queryKeys.recoveryKey });
    },
  });
};
