declare module 'react-fancy-qrcode' {
  import { ComponentType } from 'react';

  export interface QRCodeProps {
    /**
     * The value to encode in the QR code
     */
    value: string;
    
    /**
     * Size of the QR code in pixels
     */
    size?: number;
    
    /**
     * Scale factor for the dots (0-1)
     */
    dotScale?: number;
    
    /**
     * Border radius for the dots (percentage or pixels)
     */
    dotRadius?: string | number;
    
    /**
     * Border radius for position markers [outer, inner]
     */
    positionRadius?: [string, string];
    
    /**
     * Error correction level
     */
    errorCorrection?: 'L' | 'M' | 'Q' | 'H';
    
    /**
     * Logo image to display in the center
     */
    logo?: any;
    
    /**
     * Background color
     */
    backgroundColor?: string;
    
    /**
     * Foreground color
     */
    color?: string;
    
    /**
     * Additional style properties
     */
    style?: any;
  }

  const QRCode: ComponentType<QRCodeProps>;
  export default QRCode;
}
