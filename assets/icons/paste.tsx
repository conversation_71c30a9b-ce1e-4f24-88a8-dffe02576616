import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function PasteIcon({ color = '#fff', size = 24, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M12.753 2c1.158 0 2.111.875 2.234 2h1.763a2.25 2.25 0 012.245 2.096L19 6.25a.75.75 0 01-.647.742L18.249 7a.75.75 0 01-.742-.647L17.5 6.25a.75.75 0 00-.648-.743L16.75 5.5h-2.132a2.244 2.244 0 01-1.865.993H9.247c-.777 0-1.461-.393-1.865-.992L5.25 5.5a.75.75 0 00-.743.648L4.5 6.25v13.505c0 .38.282.693.648.743l.102.007h3a.75.75 0 01.743.647l.007.102a.75.75 0 01-.75.75h-3a2.25 2.25 0 01-2.245-2.095L3 19.755V6.25a2.25 2.25 0 012.096-2.245L5.25 4h1.763a2.247 2.247 0 012.234-2h3.506zm5.997 6a2.25 2.25 0 012.245 2.096l.005.154v9.5a2.25 2.25 0 01-2.096 2.245L18.75 22h-6.5a2.25 2.25 0 01-2.245-2.096L10 19.75v-9.5a2.25 2.25 0 012.096-2.245L12.25 8h6.5zm-5.997-4.5H9.247a.747.747 0 000 1.493h3.506a.747.747 0 100-1.493z'
        fill={color}
        fillRule='nonzero'
        stroke='none'
        strokeWidth={1}
      />
    </Svg>
  );
}

export default PasteIcon;
