import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function ArrowBottomLeftIcon({ color = '#fff', size = 20, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 20 20' fill='none' {...props}>
      <Path
        stroke={color}
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M15.657 4.343L4.343 15.657m0 0v-9.9m0 9.9h9.9'
      />
    </Svg>
  );
}

export default ArrowBottomLeftIcon;
