import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function ArrowTopRightIcon({ color = '#fff', size = 20, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 20 20' fill='none' {...props}>
      <Path
        stroke={color}
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M4.343 15.657L15.657 4.343m0 0v9.9m0-9.9h-9.9'
      />
    </Svg>
  );
}

export default ArrowTopRightIcon;
