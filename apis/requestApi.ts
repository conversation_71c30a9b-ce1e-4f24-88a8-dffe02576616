import { env } from '@/utils/env';

export const requestApi = async <T>({
  path,
  method,
  body,
}: {
  path: string;
  method: 'POST' | 'GET' | 'PUT' | 'DELETE' | 'PATCH';
  body?: Record<string, unknown>;
}): Promise<T> => {
  const url = env.API_URL + path;

  const { data: hash } = await fetch(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
    body: body ? JSON.stringify(body) : undefined,
  }).then((res) => res.json());

  return hash;
};
