import DeviceInfo from 'react-native-device-info';
import { Address, Hex } from 'viem';
import { env } from '@/utils/env';
import { encodeKey } from '@/utils/web3';

export const sponsorRequest = async ({
  txData,
  to,
  serializedAuth,
}: {
  txData: Hex;
  to: Address;
  serializedAuth: string;
}) => {
  const url = env.API_URL + '/api/v1/recovery/execute-sponsor';
  const deviceId = await DeviceInfo.getUniqueId();
  const encodedKey = encodeKey(env.API_PUBLIC_KEY, deviceId);

  const { data: hash } = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-encoded-key': encodedKey,
      'x-device-id': deviceId,
    },
    body: JSON.stringify({
      transaction: txData,
      authorization: serializedAuth,
      to,
    }),
  }).then((res) => res.json());

  return hash;
};
