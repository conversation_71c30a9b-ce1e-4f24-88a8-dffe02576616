import { Redirect, Stack } from 'expo-router';
import { Title } from '@/components/ui/Title';
import { Colors } from '@/constants/Colors';
import { useGetWallet } from '@/hooks/useGetWallet';
import { useCommonStore } from '@/store/common';
import { SafeProvider } from '@/utils/safeProvider';

export default function AppLayout() {
  const isFirstTime = useCommonStore.use.isFirstTime();
  const { data: wallet, isPending } = useGetWallet();
  const { privateKey, seedPhrase } = wallet || {};

  if (isPending) return null;

  if (isFirstTime || !privateKey || !seedPhrase) {
    return <Redirect href='/onboarding' />;
  }

  return (
    <SafeProvider>
      <Stack
        initialRouteName='(tabs)'
        screenOptions={{
          headerStyle: { backgroundColor: Colors.dark.background },
          contentStyle: { backgroundColor: Colors.dark.background },
          headerTitleAlign: 'center',
          headerTintColor: '#fff',
          headerBackButtonDisplayMode: 'minimal',
          headerBackButtonMenuEnabled: false,
          headerBackTitle: 'Back',
        }}
      >
        <Stack.Screen name='(tabs)' options={{ title: '', headerShown: false }} />

        <Stack.Screen name='receive' options={{ title: 'Receive', headerTitle: () => <Title fontSize={20} /> }} />

        <Stack.Screen name='send-token' options={{ title: 'Send token', headerTitle: () => <Title fontSize={20} /> }} />
      </Stack>
    </SafeProvider>
  );
}
