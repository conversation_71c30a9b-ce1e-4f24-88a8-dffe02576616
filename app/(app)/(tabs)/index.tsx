import { Home } from '@/screens/home';

// export default function HomeScreen() {
//   const { styles } = useStyles();
//   const navigation = useNavigation();

//   const { data: wallet } = useGetWallet();
//   const { data: recoveryKey } = useGetRecoveryKey();
//   const { safeWallet } = useWalletStore();
//   const { safeInstance } = useSafeContext();
//   const setSafeWallet = useWalletStore.use.setSafeWallet();

//   const { mutateAsync: resetWallet, isPending } = useResetWallet();
//   const { data: _signers = [] } = useQuery({
//     queryKey: ['signers'],
//     queryFn: async () => {
//       // TODO: Get signers
//       if (!safeInstance) return [];
//       const owners = await safeInstance?.getOwners();
//       return owners;
//     },
//     enabled: !!safeInstance,
//   });

//   const handleCopy = async (text: string) => {
//     if (!text) return;

//     await Clipboard.setStringAsync(text);
//     toastSuccess('Copied to clipboard');
//   };

//   const handleResetWallet = async () => {
//     await resetWallet();
//     setSafeWallet(null);
//     toastSuccess('Wallet reset successfully');
//     navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'onboarding' }] }));
//   };

//   const parseWallet = pkToAccount(`0x${wallet?.privateKey}` as const);

//   return (
//     <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
//       <View style={styles.containerKey}>
//         <View style={styles.boxKey}>
//           <ThemedText type='tinyLight' style={styles.keyTitle}>
//             GeoSafe wallet address
//           </ThemedText>

//           <ThemedText>{safeWallet}</ThemedText>
//         </View>

//         <CustomButton type='secondary' onPress={() => handleCopy(safeWallet ?? '')}>
//           <Icons.Copy size={20} color='#fff' />
//         </CustomButton>
//       </View>

//       <View style={styles.containerKey}>
//         <View style={styles.boxKey}>
//           <ThemedText type='tinyLight' style={styles.keyTitle}>
//             Signer wallet address
//           </ThemedText>

//           <ThemedText>{parseWallet?.address}</ThemedText>
//         </View>

//         <CustomButton type='secondary' onPress={() => handleCopy(parseWallet?.address)}>
//           <Icons.Copy size={20} color='#fff' />
//         </CustomButton>
//       </View>

//       <View style={styles.containerKey}>
//         <View style={styles.boxKey}>
//           <ThemedText type='tinyLight' style={styles.keyTitle}>
//             Recovery key
//           </ThemedText>

//           <ThemedText>{recoveryKey}</ThemedText>
//         </View>

//         <CustomButton type='secondary' onPress={() => handleCopy(recoveryKey ?? '')}>
//           <Icons.Copy size={20} color='#fff' />
//         </CustomButton>
//       </View>

//       <CustomButton type='primary' onPress={handleResetWallet} isLoading={isPending}>
//         Reset wallet
//       </CustomButton>
//     </ScrollView>
//   );
// }

// const useStyles = () => {
//   const backgroundColor = useTheme('background');
//   const white05 = useTheme('white05');
//   const white15 = useTheme('white15');
//   const white35 = useTheme('white35');

//   const styles = StyleSheet.create({
//     container: {
//       flex: 1,
//       backgroundColor: backgroundColor,
//       padding: 16,
//     },
//     contentContainer: {
//       gap: 16,
//     },
//     containerKey: {
//       backgroundColor: white05,
//       borderWidth: 1,
//       borderColor: white15,
//       borderRadius: 16,
//       padding: 16,
//       gap: 16,
//       flexDirection: 'row',
//       alignItems: 'center',
//     },
//     boxKey: {
//       flex: 1,
//       flexDirection: 'column',
//       gap: 2,
//     },
//     keyTitle: {
//       color: white35,
//     },
//   });

//   return { styles };
// };

export default Home;
