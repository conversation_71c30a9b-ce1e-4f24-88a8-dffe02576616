import { Tabs } from 'expo-router';
import { Platform, StyleSheet } from 'react-native';
import { Icons } from '@/assets/icons';
import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useTheme } from '@/hooks/useThemeColor';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { styles } = useStyles();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'dark'].tint,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }),
        headerStyle: styles.headerStyle,
        headerTitleAlign: 'center',
        headerTintColor: '#fff',
        headerBackButtonDisplayMode: 'minimal',
        sceneStyle: { backgroundColor: Colors.dark.background },
      }}
    >
      <Tabs.Screen
        name='index'
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) =>
            focused ? <Icons.HomeFill size={28} color={color} /> : <Icons.Home size={28} color={color} />,
          headerShown: false,
        }}
      />

      <Tabs.Screen
        name='list-assets'
        options={{
          title: 'Assets',
          tabBarIcon: ({ color }) => <Icons.Token size={28} color={color} />,
        }}
      />

      <Tabs.Screen
        name='transactions'
        options={{
          title: 'Transactions',
          tabBarIcon: ({ color }) => <Icons.Transaction size={28} color={color} />,
        }}
      />

      <Tabs.Screen
        name='setting'
        options={{
          title: 'Setting',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name='gear.circle' color={color} />,
        }}
      />
    </Tabs>
  );
}

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
