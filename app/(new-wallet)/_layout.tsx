import { Stack } from 'expo-router';
import { StyleSheet } from 'react-native';
import { CircleProgress } from '@/components/ui/CircleProgress';
import { useTheme } from '@/hooks/useThemeColor';

function NewWalletLayout() {
  const { styles } = useStyles();

  return (
    <Stack
      screenOptions={{
        headerStyle: styles.headerStyle,
        contentStyle: styles.container,
        headerTitleAlign: 'center',
        headerTintColor: '#fff',
        headerBackButtonDisplayMode: 'minimal',
        headerBackButtonMenuEnabled: false,
        headerBackTitle: 'Back',
      }}
    >
      <Stack.Screen name='set-up-security' />

      <Stack.Screen
        name='set-up-pin'
        options={{
          title: 'Account creation',
          headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={100 / 3} innerText='1/3' />,
        }}
      />

      <Stack.Screen
        name='set-up-biometric'
        options={{
          title: 'Account creation',
          headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
        }}
      />

      <Stack.Screen
        name='set-up-notification'
        options={{
          title: 'Account creation',
          headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
        }}
      />

      <Stack.Screen
        name='set-up-location'
        options={{
          title: 'Account creation',
          headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={200 / 3} innerText='2/3' />,
        }}
      />

      <Stack.Screen
        name='set-up-recovery-key'
        options={{
          title: 'Account creation',
          headerRight: () => <CircleProgress radius={22} borderWidth={1.5} percentage={100} innerText='3/3' />,
        }}
      />

      <Stack.Screen name='account-creation-success' options={{ headerShown: false }} />

      <Stack.Screen name='set-up-wallet' options={{ title: 'Set up wallet' }} />
    </Stack>
  );
}

export default NewWalletLayout;

const useStyles = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: backgroundColor,
    },
    headerStyle: {
      backgroundColor: backgroundColor,
    },
  });

  return { styles };
};
