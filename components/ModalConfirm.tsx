import { StyleSheet, View } from 'react-native';
import { useTheme } from '@/hooks/useThemeColor';
import { CustomButton } from './Button';
import { Spacer } from './Spacer';
import { ThemedText } from './ThemedText';
import { ModalRn } from './ui/Modal';

type Props = {
  title: string;
  isVisible: boolean;
  description: string;
  onCancel: () => void;
  onConfirm: () => void;
};

export const ModalConfirm = ({ title, isVisible, description, onCancel, onConfirm }: Props) => {
  const { styles } = useStyle();

  return (
    <ModalRn isVisible={isVisible} style={styles.container} contentStyle={styles.content}>
      <View style={styles.content}>
        <ThemedText type='defaultSemiBold' center>
          {title}
        </ThemedText>

        <ThemedText center type='tinyLight'>
          {description}
        </ThemedText>

        <View>
          <CustomButton type='secondary' size='sm' onPress={onCancel}>
            Cancel
          </CustomButton>

          <Spacer height={16} />

          <CustomButton size='sm' onPress={onConfirm}>
            Confirm
          </CustomButton>
        </View>
      </View>
    </ModalRn>
  );
};

const useStyle = () => {
  const Neutrals800 = useTheme('Neutrals/800');

  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
      paddingHorizontal: 16,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    content: {
      backgroundColor: Neutrals800,
      padding: 16,
      borderRadius: 16,
      gap: 16,
      width: '100%',
    },
  });

  return { styles };
};
