import * as Haptics from 'expo-haptics';
import * as LocalAuthentication from 'expo-local-authentication';
import { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import QuickCrypto from 'react-native-quick-crypto';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { useGetBiometric } from '@/hooks/useGetBiometric';
import { useGetPin } from '@/hooks/useGetPin';
import { useTheme } from '@/hooks/useThemeColor';
import { toastError } from '@/utils/toast';
import { NumberKeyboard } from './NumberKeyboard';
import { Spacer } from './Spacer';
import { ThemedText } from './ThemedText';
import { ModalRn } from './ui/Modal';

type Props = {
  isVisible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
};

export const ModalConfirmPinCode = ({ isVisible, onCancel, onConfirm }: Props) => {
  const { styles } = useStyle();

  const { data: hashingPassCode } = useGetPin();
  const { data: biometricEnabled } = useGetBiometric();
  const [pin, setPin] = useState('');
  const offset = useSharedValue<number>(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: offset.value }],
  }));

  const handlePressNum = (num: number) => {
    if (pin.length === 6) return;
    setPin((prev) => prev + num);
  };

  const handlePressBackspace = () => {
    setPin((prev) => prev.slice(0, -1));
  };

  useEffect(() => {
    if (!isVisible) {
      setPin('');
    }
  }, [isVisible]);

  useEffect(() => {
    if (pin.length !== 6 || !isVisible) return;

    (async () => {
      try {
        const hashingPassCodeCompare = QuickCrypto.createHash('sha256').update(pin).digest('hex');
        const isMatch = hashingPassCode === hashingPassCodeCompare;

        if (isMatch) {
          onConfirm();
          onCancel();
        } else {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          setPin('');

          const OFFSET = 10;
          const TIME = 100;

          offset.value = withSequence(
            withTiming(-OFFSET, { duration: TIME / 2 }),
            withRepeat(withTiming(OFFSET, { duration: TIME }), 2, true),
            withTiming(0, { duration: TIME / 2 })
          );
        }
      } catch (error) {
        toastError(error);
      }
    })();
  }, [pin, hashingPassCode, offset, isVisible, onCancel, onConfirm]);

  useEffect(() => {
    if (!biometricEnabled || !isVisible) return;

    (async () => {
      try {
        const biometricResult = await LocalAuthentication.authenticateAsync({
          disableDeviceFallback: true,
        });

        if (biometricResult.success) {
          onConfirm();
          onCancel();
        }
      } catch (error) {
        toastError(error);
      }
    })();
  }, [biometricEnabled, isVisible, onCancel, onConfirm]);

  return (
    <ModalRn isVisible={isVisible} style={styles.container} contentStyle={styles.content} onBackdropPress={onCancel}>
      <View>
        <ScrollView style={styles.contentContainer} contentContainerStyle={styles.contentContainerStyle}>
          <ThemedText type='title' style={styles.title}>
            Enter PIN
          </ThemedText>

          <Spacer height={24} />

          <View>
            <Animated.View style={[styles.dotContainer, animatedStyle]}>
              {Array(6)
                .fill(0)
                .map((_, index) => (
                  <View key={index} style={[styles.dot, index < pin?.length && styles.dotActive]} />
                ))}
            </Animated.View>
          </View>

          <Spacer height={24} />

          <View style={styles.keyboardContainer}>
            <NumberKeyboard onPressNum={handlePressNum} onPressBackspace={handlePressBackspace} />
          </View>
          <Spacer height={24} />
        </ScrollView>
      </View>
    </ModalRn>
  );
};

const useStyle = () => {
  const backgroundColor = useTheme('background');

  const styles = StyleSheet.create({
    container: {
      justifyContent: 'flex-end',
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    content: {
      backgroundColor,
      padding: 16,
      borderRadius: 16,
      gap: 16,
      width: '100%',
      // height: '100%',
    },
    dotActive: {
      borderWidth: 0,
      backgroundColor: '#fff',
    },
    dot: {
      width: 18,
      height: 18,
      borderWidth: 1,
      borderColor: '#B7B7C7',
      borderRadius: 999,
      overflow: 'hidden',
    },
    dotContainer: {
      flexDirection: 'row',
      gap: 12,
      justifyContent: 'center',
    },
    contentContainer: {
      padding: 24,
    },
    contentContainerStyle: {
      paddingHorizontal: 16,
      flexGrow: 1,
    },
    title: {
      textAlign: 'center',
    },
    description: {
      // color: white90,
      textAlign: 'center',
    },
    actions: {
      padding: 10,
      gap: 16,
    },
    keyboardContainer: {},
  });

  return { styles };
};
