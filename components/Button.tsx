import { ActivityIndicator, StyleSheet, TextStyle, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';
import { Spacer } from './Spacer';
import { ThemedText, ThemedTextProps } from './ThemedText';
import { LoadingSpinner } from './ui/LoadingSpinner';

export type CustomButtonProps = TouchableOpacityProps & {
  isLoading?: boolean;
  textStyle?: TextStyle;
  textType?: ThemedTextProps['type'];
  type?: 'primary' | 'secondary' | 'outlined';
  size?: 'sm' | 'md';
};

export const CustomButton = ({
  type = 'primary',
  style,
  children,
  isLoading,
  disabled,
  textStyle,
  textType = 'default',
  size = 'md',
  ...rest
}: CustomButtonProps) => {
  const { styles } = useStyles();

  return (
    <TouchableOpacity
      style={[
        styles.button,
        type === 'primary' ? styles.buttonPrimary : undefined,
        type === 'secondary' ? styles.buttonSecondary : undefined,
        type === 'outlined' ? styles.buttonOutlined : undefined,
        isLoading || disabled ? { opacity: 0.5 } : undefined,
        size === 'sm' ? styles.buttonSm : undefined,
        size === 'md' ? styles.buttonMd : undefined,
        style,
      ]}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
      {...rest}
    >
      {isLoading && (
        <>
          <ActivityIndicator color='#000' size='small' />

          <LoadingSpinner type={type} />

          {children && <Spacer width={16} />}
        </>
      )}
      {typeof children === 'string' ? (
        <ThemedText
          style={[
            styles.buttonText,
            type === 'primary' ? styles.textPrimary : undefined,
            type === 'secondary' ? styles.textSecondary : undefined,
            type === 'outlined' ? styles.textOutlined : undefined,
            textStyle,
          ]}
          type={textType}
          numberOfLines={1}
        >
          {children}
        </ThemedText>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');
  const secondary = useTheme('secondary');
  const textButtonPrimary = useTheme('textButtonPrimary');
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    button: {
      paddingHorizontal: 12,
      borderRadius: 8,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    buttonMd: {
      minHeight: 48,
    },
    buttonSm: {
      minHeight: 38,
    },
    buttonPrimary: {
      backgroundColor: primary,
    },
    buttonSecondary: {
      backgroundColor: secondary,
    },
    buttonOutlined: {
      borderWidth: 1,
      borderColor: primary,
    },
    buttonText: {
      textAlign: 'center',
      ...FontWeight.semiBold,
    },
    textPrimary: {
      color: textButtonPrimary,
    },
    textSecondary: {
      color: white65,
    },
    textOutlined: {
      color: primary,
    },
  });

  return { styles };
};
