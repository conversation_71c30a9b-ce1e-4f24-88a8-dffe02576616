import { useEffect } from 'react';
import { type StyleProp, View, type ViewStyle } from 'react-native';
import Animated, { Easing, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';
import Svg, { Circle } from 'react-native-svg';

type SizeType = 'xl' | 'lg' | 'md' | 'sm' | 'xs' | 'xxs';

const SpinnerSize = {
  sm: 12,
  md: 16,
  lg: 24,
  xl: 32,
};

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

export interface LoadingSpinnerProps {
  size?: Exclude<SizeType, 'xs' | 'xxs'>;
  color?: 'primary' | 'secondary';
  style?: StyleProp<ViewStyle>;
  testID?: string;
}

const Stroke = {
  primary: '#0C0C12',
  secondary: '#fff',
};

export function LoadingSpinner({
  color = 'primary',
  style,
  size = 'lg',
  testID = 'loading-spinner',
}: LoadingSpinnerProps) {
  const spinValue = useSharedValue(0);
  const strokeColor = Stroke[color];

  useEffect(() => {
    spinValue.value = withRepeat(
      withTiming(360, {
        duration: 200,
        easing: Easing.linear,
      }),
      -1
    );
  }, [spinValue]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${spinValue.value}deg` }],
    };
  });

  return (
    <View
      style={[
        {
          alignItems: 'center',
          justifyContent: 'center',
        },
        style,
      ]}
      testID={testID}
    >
      <AnimatedSvg width={SpinnerSize[size]} height={SpinnerSize[size]} viewBox='25 25 50 50' style={animatedStyle}>
        <Circle
          r={20}
          cy={50}
          cx={50}
          stroke={strokeColor}
          strokeWidth={4}
          fill='transparent'
          strokeDasharray={`90 124`}
        />
      </AnimatedSvg>
    </View>
  );
}
