import { PropsWithChildren } from 'react';
import { Modal, ModalBaseProps, Pressable, StyleSheet, View, ViewStyle } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

type Props = {
  withInput?: boolean;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  backdropColor?: string;
  isVisible: boolean;
  animationType?: ModalBaseProps['animationType'];
  onBackdropPress?: () => void;
};

export const ModalRn = ({
  withInput = false,
  style,
  contentStyle,
  children,
  backdropColor = 'transparent',
  isVisible,
  animationType = 'fade',
  onBackdropPress,
}: PropsWithChildren<Props>) => {
  const { styles } = useStyles();

  const content = withInput ? (
    <KeyboardAvoidingView style={[styles.container, { backgroundColor: backdropColor }, style]} behavior='padding'>
      {children}
    </KeyboardAvoidingView>
  ) : (
    <Pressable style={StyleSheet.absoluteFill} onPress={onBackdropPress}>
      <View style={[styles.content, { backgroundColor: backdropColor }, style]}>
        <Pressable style={contentStyle}>{children}</Pressable>
      </View>
    </Pressable>
  );

  return (
    <Modal transparent animationType={animationType} visible={isVisible}>
      {content}
    </Modal>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {},
  });

  return { styles };
};
