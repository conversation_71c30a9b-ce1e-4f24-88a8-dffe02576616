import { PropsWithChildren } from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { useTheme } from '@/hooks/useThemeColor';

type Props = ViewProps;

export const Card = ({ children, style, ...otherProps }: PropsWithChildren<Props>) => {
  const { styles } = useStyles();

  return (
    <View style={[styles.container, style]} {...otherProps}>
      {children}
    </View>
  );
};

const useStyles = () => {
  const cardBg = useTheme('card');

  const styles = StyleSheet.create({
    container: {
      backgroundColor: cardBg,
      padding: 16,
      borderRadius: 16,
    },
  });

  return {
    styles,
  };
};
