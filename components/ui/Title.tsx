import { StyleSheet } from 'react-native';
import { FontWeight } from '@/constants/Colors';
import { useTheme } from '@/hooks/useThemeColor';
import { ThemedText } from '../ThemedText';

type Props = {
  fontSize?: number;
};

export const Title = ({ fontSize = 32 }: Props) => {
  const { styles } = useStyles();

  return (
    <ThemedText style={[styles.title, { fontSize }]} type='title'>
      Geo<ThemedText style={[styles.title, styles.safeTitle, { fontSize }]}>Safe</ThemedText>
    </ThemedText>
  );
};
const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    termsCondition: {
      color: primary,
      textDecorationStyle: 'solid',
      textDecorationLine: 'underline',
    },
    title: {
      ...FontWeight.semiBold,
      textAlign: 'center',
      color: '#fff',
    },
    safeTitle: {
      color: primary,
    },
  });

  return { styles };
};
