import bigDecimal from 'js-big-decimal';
import { StyleSheet, View } from 'react-native';
import { formatUnits } from 'viem';
import { useTheme } from '@/hooks/useThemeColor';
import { CURRENT_CHAIN } from '@/utils/web3';
import { ThemedText } from '../ThemedText';
import { CustomImage } from './Image';

type Props = {
  logoUri: string;
  name: string;
  symbol: string;
  balance: string;
  decimal: number;
};

export const Asset = ({ logoUri, name, symbol, balance, decimal }: Props) => {
  const { styles } = useStyles();

  const isNative = !symbol && !name && !decimal;
  const formatBalance = formatUnits(BigInt(balance), isNative ? CURRENT_CHAIN.nativeCurrency.decimals : decimal);

  return (
    <View style={styles.container}>
      <View style={styles.tokenContainer}>
        <CustomImage source={{ uri: logoUri }} style={styles.logo} />

        <View>
          <ThemedText type='defaultSemiBold'>{isNative ? CURRENT_CHAIN.nativeCurrency.name : name}</ThemedText>
          <ThemedText type='smallLight'>{isNative ? CURRENT_CHAIN.nativeCurrency.symbol : symbol}</ThemedText>
        </View>
      </View>

      <ThemedText type='defaultSemiBold'>{bigDecimal.getPrettyValue(formatBalance)}</ThemedText>
    </View>
  );
};

const useStyles = () => {
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 8,
    },
    tokenContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    logo: {
      width: 36,
      height: 36,
      backgroundColor: white35,
      borderRadius: 999,
    },
  });

  return { styles };
};
