import { StyleSheet, View } from 'react-native';
import { StepProgress } from '@/components/StepProgress';
import { ThemedText } from '@/components/ThemedText';

import { useTheme } from '@/hooks/useThemeColor';
import { StepAction } from '@/screens/set-up-security/components/StepAction';

type Props = {
  hasCodeBook: boolean;
  hasLocation: boolean;
};

export const RecoverySteps = ({ hasCodeBook, hasLocation }: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <View style={styles.step}>
        <View style={styles.stepBox}>
          <StepProgress isActive />
        </View>

        <View style={styles.stepContainer}>
          <View style={styles.titleBox}>
            <ThemedText type='defaultSemiBold'>Recovery wallet</ThemedText>

            {/* <Show when={isComplete}>
                      <DoneText />
                    </Show> */}
          </View>

          <StepAction hasDone={hasCodeBook} title='Prepare code-book' description='Get code-book information' />

          <StepAction hasDone={hasLocation} title='Get location' description='Get your current location' />

          <StepAction hasDone={false} title='Recover wallet' description='Recover your wallet' />
        </View>
      </View>
    </View>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    stepContainer: {
      gap: 8,
      flex: 1,
    },
    titleBox: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    step: {
      flexDirection: 'row',
      alignItems: 'stretch',
    },
    stepActive: {
      color: primary,
    },
    stepBox: {
      flexDirection: 'row',
      height: '100%',
      paddingLeft: 16,
      paddingRight: 12,
      paddingTop: 4,
    },
  });

  return { styles };
};
