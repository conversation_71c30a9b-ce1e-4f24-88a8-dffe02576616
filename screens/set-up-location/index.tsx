import * as Location from 'expo-location';
import { Href, router } from 'expo-router';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/hooks/useThemeColor';
import { toastError } from '@/utils/toast';

type Props = {
  nextScreen: Href;
};

export const SetUpLocation = ({ nextScreen }: Props) => {
  const { styles } = useStyles();

  const requestLocation = async () => {
    try {
      let { status } = await Location.requestForegroundPermissionsAsync();

      if (status === Location.PermissionStatus.GRANTED) {
        router.replace(nextScreen);
      }
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView bounces={false} showsVerticalScrollIndicator={false}>
        <Spacer height={81} />

        <Icons.Notification size={56} />

        <Spacer height={24} />

        <ThemedText type='title'>Enable Location</ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight'>
          Secure your account and transactions with safe guarding selected locations
        </ThemedText>

        <Spacer height={24} />

        <ThemedText type='smallMedium'>Why we need it?</ThemedText>

        <ThemedText type='smallLight'>- Secure your wallet activity</ThemedText>
        <ThemedText type='smallLight'>- Prevent unauthorized access</ThemedText>
        <ThemedText type='smallLight'>- blah blah</ThemedText>
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.disclaimerContainer}>
        <ThemedText type='smallMedium'>Disclaimer</ThemedText>

        <ThemedText type='tinyLight'>
          We will not share your data or biometric information with any 3rd party. Your information is used locally to
          access your account.
        </ThemedText>
      </View>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' onPress={requestLocation}>
          Enable Location
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white04 = useTheme('white04');

  const styles = StyleSheet.create({
    disclaimerContainer: {
      backgroundColor: white04,
      borderRadius: 16,
      padding: 16,
    },
    container: {
      flex: 1,
      padding: 10,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
  });

  return { styles };
};
