import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { StyleSheet } from 'react-native';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { useTheme } from '@/hooks/useThemeColor';
import { ListNFTs } from './components/ListNFTs';
import { ListTokens } from './components/ListTokens';

const AssetTab = createMaterialTopTabNavigator();

type Props = {};

export const ListAssets = (props: Props) => {
  const { colors } = useStyles();

  return (
    <ViewSafeArea edges={[]}>
      <AssetTab.Navigator
        screenOptions={{
          tabBarStyle: { backgroundColor: 'transparent' },
          sceneStyle: { backgroundColor: 'transparent' },
          tabBarIndicatorStyle: { backgroundColor: colors.primary },
          tabBarActiveTintColor: '#fff',
          tabBarLabelStyle: { fontSize: 16, fontWeight: 'bold' },
        }}
      >
        <AssetTab.Screen name='Tokens' component={ListTokens} />

        <AssetTab.Screen name='NFTs' component={ListNFTs} />
      </AssetTab.Navigator>
    </ViewSafeArea>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      gap: 16,
    },
  });

  return { styles, colors: { primary } };
};
