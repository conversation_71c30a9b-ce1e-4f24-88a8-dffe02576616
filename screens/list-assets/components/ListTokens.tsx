import { FlatList, Image, RefreshControl, StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Asset } from '@/components/ui/Asset';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';

type Props = {};

export const ListTokens = (props: Props) => {
  const { styles } = useStyles();

  const {
    data: safeTokens,
    fetchNextPage,
    isFetchingNextPage,
    refetch: refetchTokens,
    isFetching: isFetchingTokens,
  } = useGetSafeBalances({ limit: 1 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results);

  const handleLoadMore = () => {
    if (isFetchingNextPage) return;
    fetchNextPage();
  };

  return (
    <FlatList
      data={tokens}
      style={styles.container}
      contentContainerStyle={{ gap: 16 }}
      keyExtractor={(item) => item.tokenAddress!}
      renderItem={({ item }) => (
        <Asset
          logoUri={item?.token?.logoUri!}
          name={item?.token?.name!}
          symbol={item?.token?.symbol!}
          balance={item?.balance!}
          decimal={item?.token?.decimals!}
        />
      )}
      ListEmptyComponent={() => (
        <View style={styles.emptyContainer}>
          <Image source={require('@/assets/images/empty-token.png')} style={styles.emptyImage} />

          <ThemedText type='smallLight'>No assets found</ThemedText>
        </View>
      )}
      refreshControl={<RefreshControl refreshing={isFetchingTokens} onRefresh={refetchTokens} tintColor={'#fff'} />}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      onEndReachedThreshold={0.5}
      onEndReached={handleLoadMore}
    />
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
      paddingVertical: 16,
    },
    emptyImage: {
      width: 50,
      height: 50,
    },
  });

  return { styles };
};
