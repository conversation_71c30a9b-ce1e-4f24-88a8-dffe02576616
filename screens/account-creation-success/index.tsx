import { CommonActions } from '@react-navigation/native';
import { useNavigation } from 'expo-router';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';

type Props = {};

export const AccountCreationSuccess = (props: Props) => {
  const { styles } = useStyles();
  const navigation = useNavigation();

  const handleNextStep = async () => {
    navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'set-up-security' }] }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        bounces={false}
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Icons.CheckCircle size={125} color={Colors.dark.primary} />

        <Spacer height={22} />

        <ThemedText type='title'>Success</ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight'>Your account has successfully been created</ThemedText>
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton type='primary' onPress={handleNextStep}>
          Next steps
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    scrollContainer: {},
    scrollContent: {
      flexGrow: 1,
      flex: 1,
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  return { styles };
};
