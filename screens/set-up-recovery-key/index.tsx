import { CommonActions } from '@react-navigation/native';
import * as Clipboard from 'expo-clipboard';
import { router, useNavigation } from 'expo-router';
import { useState } from 'react';
import { FlatList, StyleSheet, Switch, View } from 'react-native';
import QuickCrypto from 'react-native-quick-crypto';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { useSaveRecoveryKeysNewWallet } from '@/hooks/useSaveRecoveryKeysNewWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { toastError, toastSuccess } from '@/utils/toast';

type Props = {};

export const SetUpRecoveryKey = (props: Props) => {
  const { styles } = useStyles();
  const navigation = useNavigation();

  const [recoveryKeys, setRecoveryKeys] = useState<string[]>([]);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [numOfKeys, setNumOfKeys] = useState('3');

  const { mutateAsync: saveRecoveryKeys } = useSaveRecoveryKeysNewWallet();

  const handleMaybeLater = () => {
    navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: 'set-up-security' }] }));
  };

  const handleNextStep = async () => {
    try {
      await saveRecoveryKeys(recoveryKeys);
      router.navigate('/account-creation-success');
    } catch (error) {
      toastError(error);
    }
  };

  const toggleConfirm = () => {
    setIsConfirmed(!isConfirmed);
  };

  const handleCopy = async (recoveryKey: string) => {
    if (!recoveryKey) return;

    await Clipboard.setStringAsync(recoveryKey);
    toastSuccess('Copied to clipboard');
  };

  const handleChangeNumOfKeys = (text: string) => {
    if (text === numOfKeys) return;

    setRecoveryKeys([]);

    if (text.length === 0) {
      setNumOfKeys('');
      return;
    }

    if (text.match(/^\d+$/)) {
      setNumOfKeys(text);
    }
  };

  const generateRecoveryKeys = () => {
    if (recoveryKeys.length === Number.parseInt(numOfKeys)) return;

    if (recoveryKeys.length > Number.parseInt(numOfKeys)) {
      setRecoveryKeys(recoveryKeys.slice(0, Number.parseInt(numOfKeys)));
    }

    const numOfKeysNeedGen = Number.parseInt(numOfKeys) - recoveryKeys.length;
    const keys: string[] = [];
    for (let i = 0; i < numOfKeysNeedGen; i++) {
      const recoveryKey = QuickCrypto.randomBytes(32).toString('hex');
      keys.push(recoveryKey);
    }

    setRecoveryKeys((prev) => [...prev, ...keys]);
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ThemedText type='title'>Recovery key</ThemedText>

      <Spacer height={16} />

      <ThemedText type='smallLight'>Copy and securely store the following recovery code</ThemedText>

      <Spacer height={16} />

      <TextInput
        label='Num of keys'
        placeholder='Enter number of keys'
        value={numOfKeys}
        onChangeText={handleChangeNumOfKeys}
        keyboardType='number-pad'
      />

      <Spacer height={16} />

      <FlatList
        bounces={true}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: 16 }}
        keyboardShouldPersistTaps='handled'
        data={recoveryKeys}
        renderItem={({ item }) => (
          <View style={styles.containerKey} key={item} pointerEvents='auto'>
            <View style={styles.boxKey} pointerEvents='auto'>
              <ThemedText type='tinyLight' style={styles.keyTitle}>
                Recovery key
              </ThemedText>

              <ThemedText>{item}</ThemedText>
            </View>

            <CustomButton type='secondary' onPress={() => handleCopy(item)}>
              <Icons.Copy size={20} color='#fff' />
            </CustomButton>
          </View>
        )}
      />

      <Show when={recoveryKeys.length > 0}>
        <Spacer height={16} />

        <View style={styles.switchContainer}>
          <Switch value={isConfirmed} onValueChange={toggleConfirm} />

          <ThemedText onPress={toggleConfirm} type='small'>
            Confirm I have secured the recovery key
          </ThemedText>
        </View>
      </Show>

      <Spacer height={16} />

      <View style={styles.actions}>
        <CustomButton
          type='primary'
          disabled={(recoveryKeys.length !== 0 && !isConfirmed) || numOfKeys.length === 0 || Number(numOfKeys) < 3}
          onPress={recoveryKeys.length === 0 ? generateRecoveryKeys : handleNextStep}
        >
          {recoveryKeys.length === 0 ? 'Generate keys' : 'Confirm to continue'}
        </CustomButton>

        <CustomButton type='secondary' onPress={handleMaybeLater}>
          Cancel
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    container: {
      flex: 1,
      padding: 10,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
  });

  return { styles };
};
