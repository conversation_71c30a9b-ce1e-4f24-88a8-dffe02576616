import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { StyleSheet } from 'react-native';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { useTheme } from '@/hooks/useThemeColor';
import { ListHistoryTransactions } from './components/ListHistoryTransactions';
import { ListPendingTransactions } from './components/ListPendingTransactions';

const AssetTab = createMaterialTopTabNavigator();

type Props = {};

export const ListTransactions = (props: Props) => {
  const { colors } = useStyles();

  return (
    <ViewSafeArea edges={[]}>
      <AssetTab.Navigator
        screenOptions={{
          tabBarStyle: { backgroundColor: 'transparent' },
          sceneStyle: { backgroundColor: 'transparent' },
          tabBarIndicatorStyle: { backgroundColor: colors.primary },
          tabBarActiveTintColor: '#fff',
          tabBarLabelStyle: { fontSize: 16, fontWeight: 'bold' },
        }}
      >
        <AssetTab.Screen name='Pending' component={ListPendingTransactions} />

        <AssetTab.Screen name='History' component={ListHistoryTransactions} />
      </AssetTab.Navigator>
    </ViewSafeArea>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      gap: 16,
    },
  });

  return { styles, colors: { primary } };
};
