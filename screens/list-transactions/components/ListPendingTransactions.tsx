import { FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { useGetSafeTxsPending } from '@/hooks/useGetSafeTxsPending';
import { Transaction } from '@/screens/home/<USER>/Transaction';

type Props = {};

export const ListPendingTransactions = (props: Props) => {
  const { styles } = useStyles();

  const {
    data: safeTxs,
    fetchNextPage,
    isFetchingNextPage,
    refetch: refetchTxs,
    isFetching: isFetchingTxs,
  } = useGetSafeTxsPending({ limit: 10 });
  const txs = safeTxs?.pages.flatMap((page) => page.results);

  const handleLoadMore = () => {
    if (isFetchingNextPage) return;
    fetchNextPage();
  };

  return (
    <FlatList
      style={styles.container}
      contentContainerStyle={{ gap: 16 }}
      keyboardShouldPersistTaps='handled'
      data={txs}
      renderItem={({ item }) => <Transaction txData={item} />}
      refreshControl={<RefreshControl refreshing={isFetchingTxs} onRefresh={refetchTxs} tintColor={'#fff'} />}
      ListEmptyComponent={() => (
        <View style={styles.emptyContainer}>
          <Icons.Transaction size={50} color='#fff' />

          <ThemedText type='smallLight'>No pending transactions found</ThemedText>
        </View>
      )}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      onEndReachedThreshold={0.5}
      onEndReached={handleLoadMore}
    />
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
      paddingVertical: 16,
    },
    emptyImage: {
      width: 50,
      height: 50,
    },
  });

  return { styles };
};
