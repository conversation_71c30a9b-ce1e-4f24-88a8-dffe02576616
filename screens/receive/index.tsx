import * as Clipboard from 'expo-clipboard';
import QRCode from 'react-fancy-qrcode';
import { Share, StyleSheet, View } from 'react-native';
import { CustomButton } from '@/components/Button';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { useSafeContext } from '@/context/safeContext';
import { toastSuccess } from '@/utils/toast';

export const Receive = () => {
  const { styles } = useStyles();
  const { safeWallet } = useSafeContext();

  async function handleCopy() {
    if (!safeWallet) return;

    await Clipboard.setStringAsync(safeWallet);
    toastSuccess('Copied to clipboard');
  }

  async function handleShare() {
    if (!safeWallet) return;

    await Share.share({
      message: safeWallet,
    });
  }

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <ThemedText type='defaultSemiBold'>Receive Assets</ThemedText>

        <ThemedText type='smallLight'>
          This is the GeoSafe address. Please deposit funds by scanning the QR code or copying the address below.
        </ThemedText>

        <View style={styles.qrCodeContainer}>
          <QRCode
            value={'https://github.com/jgillick/react-fancy-qrcode'}
            size={200}
            dotScale={0.8}
            dotRadius='50%'
            positionRadius={['3%', '1%']}
            errorCorrection='H'
            // logo={require('images/fire.png')}
          />
        </View>

        <ThemedText type='tinyLight' center>
          <ThemedText type='tinyMedium'>sep:</ThemedText>
          {safeWallet}
        </ThemedText>
      </Card>

      <View style={styles.actions}>
        <CustomButton
          type='outlined'
          size='sm'
          onPress={handleShare}
          textType='tinyLight'
          style={styles.buttonContainer}
        >
          Share
        </CustomButton>

        <CustomButton type='primary' size='sm' onPress={handleCopy} textType='tinyLight' style={styles.buttonContainer}>
          Copy Address
        </CustomButton>
      </View>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      gap: 16,
    },
    qrCodeContainer: {
      padding: 10,
      backgroundColor: '#fff',
      marginHorizontal: 'auto',
      borderRadius: 10,
    },
    card: {
      gap: 16,
    },
    actions: {
      flexDirection: 'row',
      gap: 8,
    },
    buttonContainer: {
      flex: 1,
      flexGrow: 1,
    },
  });

  return { styles };
};
