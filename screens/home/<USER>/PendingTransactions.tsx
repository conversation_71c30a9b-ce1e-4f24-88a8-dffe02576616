import { FlatList, StyleSheet, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { useGetSafeTxsPending } from '@/hooks/useGetSafeTxsPending';
import { CardTitle } from './CardTitle';
import { Transaction } from './Transaction';

type Props = {};

export const PendingTransactions = (props: Props) => {
  const { styles } = useStyles();

  const { data: safeTxs } = useGetSafeTxsPending({ limit: 5 });
  const txs = safeTxs?.pages.flatMap((page) => page.results);

  return (
    <Card style={styles.container}>
      <CardTitle title='Pending Transactions' directLink='/(app)/(tabs)/transactions' />

      <FlatList
        bounces={true}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: 16 }}
        keyboardShouldPersistTaps='handled'
        data={txs}
        renderItem={({ item }) => <Transaction txData={item} />}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Icons.Transaction size={50} color='#fff' />

            <ThemedText type='smallLight'>No pending transactions</ThemedText>
          </View>
        )}
      />
    </Card>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
    },
  });

  return { styles };
};
