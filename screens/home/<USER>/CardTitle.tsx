import { Href, router } from 'expo-router';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';

type Props = {
  title: string;
  directLink?: Href;
};

export const CardTitle = ({ title, directLink }: Props) => {
  const { styles } = useStyles();

  const handleDirect = () => {
    if (!directLink) return;
    router.push(directLink);
  };

  return (
    <View style={styles.container}>
      <ThemedText type='defaultSemiBold'>{title}</ThemedText>

      <Show when={!!directLink}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleDirect}>
          <ThemedText type='tinyLight'>See all</ThemedText>
        </TouchableOpacity>
      </Show>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
  });

  return { styles };
};
