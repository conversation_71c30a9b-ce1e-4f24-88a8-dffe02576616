import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, { FadeIn, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { formatDateToNow } from '@/utils/date';
import {
  classifyByMethod,
  classifyByOperation,
  classifyByTypeAndValue,
  determineCategory,
} from '@/utils/safeTransaction';
import { TransactionDetail } from './TransactionDetail';
import { TransactionType } from './TransactionType';
import { TransferAmountInfo } from './TransferAmountInfo';

type Props = {
  txData: SafeMultisigTransactionResponse;
};

export const Transaction = ({ txData }: Props) => {
  const { styles } = useStyles();
  const [isOpen, setIsOpen] = useState(false);

  const operationType = classifyByOperation(txData);
  const valueType = classifyByTypeAndValue(txData);
  const methodType = classifyByMethod(txData);

  const category = determineCategory(valueType, methodType);

  const rotateAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: withSpring(isOpen ? '180deg' : '0deg', {
            stiffness: 200,
            damping: 100,
          }),
        },
      ],
    };
  });

  return (
    <View>
      <TouchableOpacity style={styles.container} onPress={() => setIsOpen((value) => !value)} activeOpacity={0.7}>
        <View>
          <ThemedText type='tinyLight'>{txData.nonce}</ThemedText>
        </View>

        <View style={styles.fullFlex}>
          <TransactionType type={operationType} category={category} />
        </View>

        <View style={styles.fullFlex}>
          <Show when={category === 'TOKEN_TRANSFER' || category === 'ETH_TRANSFER' || category === 'BATCH_OPERATION'}>
            <TransferAmountInfo txData={txData} />
          </Show>

          <ThemedText type='tinyLight' center>
            {txData.confirmations?.length}/{txData.confirmationsRequired} confirmation
          </ThemedText>
        </View>

        <View style={styles.fullFlex}>
          <ThemedText type='tinyLight' center>
            {formatDateToNow(txData.submissionDate)}
          </ThemedText>
        </View>

        <View style={[{ alignItems: 'flex-end' }]}>
          <TouchableOpacity onPress={() => setIsOpen((value) => !value)} activeOpacity={0.7}>
            <Animated.View style={rotateAnimatedStyle}>
              <Icons.ChevronDown size={24} color='#fff' />
            </Animated.View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      <Show when={isOpen}>
        <Animated.View entering={FadeIn}>
          <TransactionDetail txData={txData} category={category} />
        </Animated.View>
      </Show>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    fullFlex: {
      flex: 1,
    },
    haftFlex: {
      flex: 0.5,
    },
  });

  return { styles };
};
