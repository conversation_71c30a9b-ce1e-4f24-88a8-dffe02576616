import { AppKitButton } from '@reown/appkit-wagmi-react-native';
import { StyleSheet } from 'react-native';
import { useAccount, useDisconnect } from 'wagmi';
import { CustomButton } from '@/components/Button';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const ExternalWalletCard = (props: Props) => {
  const { styles } = useStyles();

  const { address } = useAccount();
  const { disconnectAsync, isPending: isDisconnecting } = useDisconnect();

  if (!address) {
    return (
      <Card style={styles.container}>
        <ThemedText center type='tinyLight'>
          You not connected to external wallet
        </ThemedText>

        <CustomButton isLoading>Connect</CustomButton>

        <AppKitButton connectStyle={styles.connectButton} accountStyle={{}} />
      </Card>
    );
  }

  return (
    <Card style={styles.container}>
      <ThemedText>External Wallet Card</ThemedText>
    </Card>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    container: {
      gap: 16,
      padding: 24,
    },
    connectButton: {
      backgroundColor: primary,
    },
  });

  return { styles };
};
