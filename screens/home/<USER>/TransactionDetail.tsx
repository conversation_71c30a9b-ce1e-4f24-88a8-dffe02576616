import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { privateKeyToAddress } from 'viem/accounts';
import { CustomButton } from '@/components/Button';
import { ModalConfirm } from '@/components/ModalConfirm';
import { ModalConfirmPinCode } from '@/components/ModalConfirmPinCode';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useSafeContext } from '@/context/safeContext';
import { useApproveSafeTx } from '@/hooks/useApproveSafeTx';
import { useDenySafeTx } from '@/hooks/useDenySafeTx';
import { useExecuteSafeTx } from '@/hooks/useExecuteSafeTx';
import { useTheme } from '@/hooks/useThemeColor';
import { determineCategory, getTokenInfoFromTransaction } from '@/utils/safeTransaction';
import { toastError, toastSuccess } from '@/utils/toast';
import { TransferTransactionDetail } from './TransferTransactionDetail';

type Props = {
  txData: SafeMultisigTransactionResponse;
  category: ReturnType<typeof determineCategory>;
};

export const TransactionDetail = ({ txData, category }: Props) => {
  const { styles } = useStyles();
  const { signer } = useSafeContext();
  const signerAddress = privateKeyToAddress(`0x${signer?.privateKey}`);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isSecureConfirmApprove, setSecureConfirmApprove] = useState(false);
  const [isSecureConfirmDeny, setSecureConfirmDeny] = useState(false);
  const [isSecureConfirmExecute, setSecureConfirmExecute] = useState(false);

  const isConfirmed =
    (txData.confirmations || [])?.findIndex((sig) => sig.owner.toLowerCase() === signerAddress.toLowerCase()) > -1;
  const isCanExecute = !txData.isExecuted && Number(txData.confirmations?.length || 0) >= txData.confirmationsRequired;

  const { isPending: isPendingApprove, mutateAsync: approveTx } = useApproveSafeTx();
  const { isPending: isPendingDeny, mutateAsync: denyTx } = useDenySafeTx();
  const { isPending: isExecuting, mutateAsync: executeTx } = useExecuteSafeTx();

  async function handleExecute() {
    try {
      await executeTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction executed successfully');
    } catch (error) {
      toastError(error);
    }
  }

  async function handleApprove() {
    try {
      await approveTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction approved successfully');
    } catch (error) {
      toastError(error);
    }
  }

  async function handleDeny() {
    try {
      setIsModalVisible(false);
      await denyTx({ safeTxHash: txData.safeTxHash });
      toastSuccess('Transaction denied successfully');
    } catch (error) {
      toastError(error);
    }
  }

  const handleCheckSecureForDenyTx = () => {
    setIsModalVisible(false);
    setSecureConfirmDeny(true);
  };

  return (
    <View>
      <Spacer height={16} />

      <Show when={category === 'ETH_TRANSFER' || category === 'TOKEN_TRANSFER' || category === 'BATCH_OPERATION'}>
        <TransferTransaction txData={txData} />
      </Show>

      <Show when={category === 'REJECTION'}>
        <RejectionTransaction txData={txData} />
      </Show>

      <Show when={!isCanExecute && !isConfirmed}>
        <Spacer height={16} />

        <View style={styles.actionContainer}>
          <CustomButton
            size='sm'
            type='primary'
            style={[styles.acceptButton, styles.fullFlex]}
            textStyle={styles.textWhite}
            textType='tinyMedium'
            isLoading={isPendingApprove}
            disabled={isPendingDeny}
            onPress={() => setSecureConfirmApprove(true)}
          >
            Approve
          </CustomButton>

          <Show when={category !== 'REJECTION'}>
            <CustomButton
              size='sm'
              type='primary'
              style={[styles.denyButton, styles.fullFlex]}
              textStyle={styles.textWhite}
              textType='tinyMedium'
              isLoading={isPendingDeny}
              disabled={isPendingApprove}
              onPress={() => setIsModalVisible(true)}
            >
              Deny
            </CustomButton>
          </Show>
        </View>
      </Show>

      <Show when={isCanExecute}>
        <Spacer height={16} />

        <CustomButton
          size='sm'
          type='primary'
          style={[styles.acceptButton, styles.fullFlex]}
          textStyle={styles.textWhite}
          textType='tinyMedium'
          isLoading={isExecuting}
          onPress={() => setSecureConfirmExecute(true)}
        >
          Execute
        </CustomButton>
      </Show>

      <ModalConfirm
        title='Reject transaction'
        isVisible={isModalVisible}
        description='Propose an on-chain cancellation transaction'
        onCancel={() => setIsModalVisible(false)}
        onConfirm={handleCheckSecureForDenyTx}
      />

      <ModalConfirmPinCode
        isVisible={isSecureConfirmApprove}
        onCancel={() => setSecureConfirmApprove(false)}
        onConfirm={handleApprove}
      />

      <ModalConfirmPinCode
        isVisible={isSecureConfirmDeny}
        onCancel={() => setSecureConfirmDeny(false)}
        onConfirm={handleDeny}
      />

      <ModalConfirmPinCode
        isVisible={isSecureConfirmExecute}
        onCancel={() => setSecureConfirmExecute(false)}
        onConfirm={handleExecute}
      />
    </View>
  );
};

const RejectionTransaction = ({ txData }: Pick<Props, 'txData'>) => {
  return (
    <View>
      <Spacer height={16} />

      <ThemedText type='tinyLight'>
        This is an on-chain rejection that won't send any funds. Executing this on-chain rejection will replace all
        currently awaiting transactions with nonce {txData.nonce}
      </ThemedText>
    </View>
  );
};

const TransferTransaction = ({ txData }: Pick<Props, 'txData'>) => {
  const tokenInfoFromTx = getTokenInfoFromTransaction(txData);
  return <TransferTransactionDetail txData={txData} tokenInfoFromTx={tokenInfoFromTx} />;
};

const useStyles = () => {
  const red = useTheme('red');
  const red30 = useTheme('red30');
  const primary = useTheme('primary');
  const primary30 = useTheme('primary30');

  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
    denyButton: {
      backgroundColor: red30,
      borderWidth: 1,
      borderColor: red,
    },
    textWhite: {
      color: '#fff',
    },
    acceptButton: {
      backgroundColor: primary30,
      borderWidth: 1,
      borderColor: primary,
    },
    actionContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
  });

  return { styles };
};
