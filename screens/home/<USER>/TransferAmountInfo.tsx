import { SafeMultisigTransactionResponse } from '@safe-global/types-kit';
import { View } from 'react-native';
import { formatUnits } from 'viem';
import { ThemedText } from '@/components/ThemedText';
import { useGetTokenInfo } from '@/hooks/useGetTokenInfo';
import { prettyNumber } from '@/utils/common';
import { getTokenInfoFromTransaction } from '@/utils/safeTransaction';

type Props = {
  txData: SafeMultisigTransactionResponse;
};

export const TransferAmountInfo = ({ txData }: Props) => {
  const tokenInfoFromTx = getTokenInfoFromTransaction(txData);

  if (!tokenInfoFromTx) return null;

  if (Array.isArray(tokenInfoFromTx))
    return (
      <View>
        <ThemedText type='smallMedium' center>
          {tokenInfoFromTx.length} {tokenInfoFromTx.length > 1 ? 'actions' : 'action'}
        </ThemedText>
      </View>
    );

  const { data: tokenInfo } = useGetTokenInfo({ tokenAddress: tokenInfoFromTx.tokenAddress });
  const formatAmount =
    tokenInfo && tokenInfoFromTx?.amount ? formatUnits(BigInt(tokenInfoFromTx?.amount), tokenInfo?.decimals || 0) : 0;

  return (
    <View>
      <ThemedText type='smallMedium' center>
        {prettyNumber(formatAmount)} {tokenInfo?.symbol}
      </ThemedText>
    </View>
  );
};
