import { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { classifyByOperation, determineCategory } from '@/utils/safeTransaction';

type Props = {
  type: ReturnType<typeof classifyByOperation>;
  category: ReturnType<typeof determineCategory>;
};

export const TransactionType = ({ type, category }: Props) => {
  const { styles } = useStyles();

  const isSend = type === 'CALL' && (category === 'ETH_TRANSFER' || category === 'TOKEN_TRANSFER');
  const isBatchSend = type === 'DELEGATE_CALL' && category === 'BATCH_OPERATION';

  const transactionType = useMemo(() => {
    if (isSend) return 'Send';
    if (isBatchSend) return 'Batch send';
    if (type === 'CALL' && category === 'REJECTION') return 'Rejection';
    if (type === 'CREATE') return 'Create';

    return 'Other';
  }, [isSend, isBatchSend, type, category]);

  return (
    <View style={styles.row}>
      <Show when={isSend || isBatchSend}>
        <Icons.ArrowTopRight size={16} color='red' />
      </Show>

      <ThemedText type='smallMedium'>{transactionType}</ThemedText>
    </View>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
  });

  return { styles };
};
