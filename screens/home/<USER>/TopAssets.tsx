import { FlatList, StyleSheet, View } from 'react-native';
import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Asset } from '@/components/ui/Asset';
import { Card } from '@/components/ui/Card';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';
import { CardTitle } from './CardTitle';

type Props = {};

export const TopAssets = (props: Props) => {
  const { styles } = useStyles();

  const { data: safeTokens } = useGetSafeBalances({ limit: 3 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results);

  return (
    <Card style={styles.container}>
      <CardTitle title='Top Assets' directLink='/(app)/(tabs)/list-assets' />

      <FlatList
        data={tokens}
        contentContainerStyle={{ gap: 16 }}
        keyExtractor={(item) => item.tokenAddress!}
        renderItem={({ item }) => (
          <Asset
            logoUri={item?.token?.logoUri!}
            name={item?.token?.name!}
            symbol={item?.token?.symbol!}
            balance={item?.balance!}
            decimal={item?.token?.decimals!}
          />
        )}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Icons.Token size={50} color='#fff' />

            <ThemedText type='smallLight'>No assets found</ThemedText>
          </View>
        )}
        scrollEnabled={false}
      />
    </Card>
  );
};

const useStyles = () => {
  const styles = StyleSheet.create({
    container: {
      gap: 16,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
    },
    emptyImage: {
      width: 50,
      height: 50,
    },
  });

  return { styles };
};
