import { isAddress } from 'viem';
import { z } from 'zod';
import { ZOD_ERRORS } from '@/utils/zodError';

// Schema for a single transfer item
const transferItemSchema = z.object({
  recipientAddress: z
    .string(ZOD_ERRORS.required)
    .trim()
    .refine((val) => isAddress(val), ZOD_ERRORS.invalidAddress),
  token: z.string(ZOD_ERRORS.required).trim(),
  amount: z
    .string(ZOD_ERRORS.required)
    .trim()
    .regex(/^\d*\.?\d*$/, 'Please enter a valid number')
    .refine((val) => {
      const num = Number.parseFloat(val);
      return !isNaN(num) && num > 0;
    }, 'Amount must be greater than 0'),
});

// Schema for the entire transfer form with array of transfers
export const transferTokenSchema = z.object({
  isConfirm: z.boolean().optional(),
  transfers: z
    .array(transferItemSchema)
    .min(1, 'At least one transfer is required')
    .max(10, 'Maximum 10 transfers allowed'),
});

// Export individual transfer item type for convenience
export type TransferItem = z.infer<typeof transferItemSchema>;

// Export the main form data type
export type TransferFormData = z.infer<typeof transferTokenSchema>;
