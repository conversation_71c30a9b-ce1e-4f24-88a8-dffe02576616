import * as Clipboard from 'expo-clipboard';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import TextInput from '@/components/ui/TextInput';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { TransferFormData } from '../transferTokenSchema';
import { ChooseAsset } from './ChooseAsset';

export const FormSendToken = () => {
  const { styles, colors } = useStyles();

  const { data: safeTokens } = useGetSafeBalances({ limit: 10 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results) || [];

  const methods = useFormContext<TransferFormData>();
  const {
    control,
    formState: { errors, isSubmitting },
  } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'transfers',
  });

  async function handlePaste(index: number) {
    const content = await Clipboard.getStringAsync();
    methods.setValue(`transfers.${index}.recipientAddress`, content, { shouldValidate: true });
  }

  function addTransfer() {
    append({
      recipientAddress: '',
      token: '',
      amount: '',
    });
  }

  function removeTransfer(index: number) {
    if (fields.length > 1) {
      remove(index);
    }
  }

  return (
    <View style={styles.container}>
      <View>
        <ThemedText type='defaultSemiBold'>New transaction</ThemedText>

        <Spacer height={8} />

        <ThemedText type='default'>
          Send tokens ({fields.length} transfer{fields.length > 1 ? 's' : ''})
        </ThemedText>
      </View>

      <View style={styles.divider} />

      <KeyboardAwareScrollView showsVerticalScrollIndicator={false} bottomOffset={16}>
        {fields.map((field, index) => (
          <Card key={field.id} style={styles.transferCard}>
            <View style={styles.transferHeader}>
              <ThemedText type='defaultSemiBold'>Transfer #{index + 1}</ThemedText>
              {fields.length > 1 && (
                <TouchableOpacity onPress={() => removeTransfer(index)} style={styles.removeButton}>
                  <ThemedText type='defaultSemiBold' style={{ color: colors.white65 }}>
                    ×
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>

            <Spacer height={12} />

            <Controller
              control={control}
              name={`transfers.${index}.recipientAddress`}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  placeholder='Enter recipient address'
                  placeholderTextColor='#666'
                  value={value}
                  label='Recipient address'
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.transfers?.[index]?.recipientAddress?.message}
                  editable={!isSubmitting}
                  onRightIconPress={() => handlePaste(index)}
                  rightIcon={<Icons.Paste size={24} color={colors.white65} />}
                />
              )}
            />

            <Spacer height={12} />

            <ChooseAsset tokens={tokens} transferIndex={index} />

            <Spacer height={12} />

            <Controller
              control={control}
              name={`transfers.${index}.amount`}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  placeholder='Enter amount'
                  placeholderTextColor='#666'
                  value={value}
                  label='Amount'
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.transfers?.[index]?.amount?.message}
                  editable={!isSubmitting}
                />
              )}
            />
          </Card>
        ))}

        <CustomButton type='secondary' onPress={addTransfer} disabled={isSubmitting || fields.length >= 10}>
          Add Another Transfer
        </CustomButton>
      </KeyboardAwareScrollView>
    </View>
  );
};

const useStyles = () => {
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
      gap: 16,
    },
    divider: {
      borderBottomWidth: 1,
      borderBottomColor: white65,
    },
    transferCard: {
      marginBottom: 8,
    },
    transferHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    removeButton: {
      padding: 4,
    },
  });

  return { styles, colors: { white65 } };
};
