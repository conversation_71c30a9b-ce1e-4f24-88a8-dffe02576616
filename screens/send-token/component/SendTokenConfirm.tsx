import { ScrollView, StyleSheet, View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { CustomImage } from '@/components/ui/Image';
import { TokenResult } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { prettyNumber } from '@/utils/common';
import { TransferItem } from '../transferTokenSchema';

type TransferWithToken = TransferItem & {
  tokenInfo?: TokenResult;
};

type Props = {
  transfers: TransferWithToken[];
};

export const SendTokenConfirm = ({ transfers }: Props) => {
  const { styles } = useStyles();

  return (
    <ScrollView bounces={false} showsVerticalScrollIndicator={false} contentContainerStyle={{ gap: 16, padding: 10 }}>
      <View>
        <ThemedText type='defaultSemiBold'>Confirm transaction</ThemedText>

        <Spacer height={8} />

        <ThemedText type='default'>
          Send tokens ({transfers.length} transfer{transfers.length > 1 ? 's' : ''})
        </ThemedText>
      </View>

      <View style={styles.divider} />

      {transfers.map((transfer, index) => (
        <Card key={index} style={styles.transferCard}>
          <View style={styles.transferHeader}>
            <ThemedText type='defaultSemiBold'>Transfer #{index + 1}</ThemedText>
            {transfer.tokenInfo?.token && (
              <View style={styles.tokenInfo}>
                <CustomImage source={{ uri: transfer.tokenInfo.token.logoUri }} style={styles.tokenLogo} />
                <ThemedText type='smallMedium'>{transfer.tokenInfo.token.symbol}</ThemedText>
              </View>
            )}
          </View>

          <Spacer height={12} />

          <View>
            <ThemedText type='defaultLight'>Recipient address</ThemedText>
            <Spacer height={4} />
            <ThemedText type='defaultSemiBold'>{transfer.recipientAddress}</ThemedText>
          </View>

          <Spacer height={12} />

          <View>
            <ThemedText type='defaultLight'>Amount</ThemedText>
            <Spacer height={4} />
            <ThemedText type='defaultSemiBold'>
              {prettyNumber(transfer.amount)} {transfer.tokenInfo?.token?.symbol || 'Unknown'}
            </ThemedText>
          </View>
        </Card>
      ))}
    </ScrollView>
  );
};

const useStyles = () => {
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 10,
      gap: 16,
    },
    divider: {
      borderBottomWidth: 1,
      borderBottomColor: white65,
    },
    center: {
      alignItems: 'center',
    },
    tokenLogo: {
      width: 24,
      height: 24,
      borderRadius: 999,
      overflow: 'hidden',
    },
    transferCard: {
      marginBottom: 8,
    },
    transferHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    tokenInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
    },
  });

  return { styles };
};
