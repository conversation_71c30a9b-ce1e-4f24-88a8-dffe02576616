import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Asset } from '@/components/ui/Asset';
import { TokenResult } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { TransferFormData } from '../transferTokenSchema';

type Props = {
  tokens: TokenResult[];
  transferIndex: number;
};

export const ChooseAsset = ({ tokens, transferIndex }: Props) => {
  const { styles } = useStyles();
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<TransferFormData>();
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const error = errors.transfers?.[transferIndex]?.token?.message;
  const hasError = !!error;
  const token = watch(`transfers.${transferIndex}.token`);
  const tokenInfo = tokens.find((t) => t.tokenAddress === token);

  const handlePress = () => {
    bottomSheetRef.current?.present();
  };

  const handleSelectToken = (token: TokenResult) => {
    setValue(`transfers.${transferIndex}.token`, token.tokenAddress!, { shouldValidate: true });
    bottomSheetRef.current?.close();
  };

  return (
    <View>
      <ThemedText type='smallLight'>Select token</ThemedText>

      <Spacer height={16} />

      <TouchableOpacity onPress={handlePress} style={[styles.assetContainer, hasError ? styles.error : null]}>
        {tokenInfo ? (
          <Asset
            logoUri={tokenInfo?.token?.logoUri ?? ''}
            name={tokenInfo?.token?.name ?? ''}
            symbol={tokenInfo?.token?.symbol ?? ''}
            balance={tokenInfo?.balance ?? ''}
            decimal={tokenInfo?.token?.decimals ?? 0}
          />
        ) : (
          <ThemedText type='smallLight' center>
            Select token
          </ThemedText>
        )}
      </TouchableOpacity>

      {hasError && typeof error === 'string' && (
        <ThemedText type='tinyLight' style={styles.errorText}>
          {error}
        </ThemedText>
      )}

      <BottomSheetModal
        ref={bottomSheetRef}
        handleStyle={styles.handleSheetStyle}
        snapPoints={['25%', '50%', '100%']}
        backgroundStyle={styles.sheetContainer}
        style={{ zIndex: 20 }}
      >
        <BottomSheetView style={[styles.sheetContainer, styles.contentContainer]}>
          <ThemedText type='defaultSemiBold'>Select token</ThemedText>

          <Spacer height={16} />

          <FlatList
            data={tokens}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleSelectToken(item)}>
                <Asset
                  logoUri={item?.token?.logoUri ?? ''}
                  name={item?.token?.name ?? ''}
                  symbol={item?.token?.symbol ?? ''}
                  balance={item?.balance ?? ''}
                  decimal={item?.token?.decimals ?? 0}
                />
              </TouchableOpacity>
            )}
            contentContainerStyle={{ gap: 16 }}
          />
        </BottomSheetView>
      </BottomSheetModal>
    </View>
  );
};

const useStyles = () => {
  const card = useTheme('card');
  const primary = useTheme('primary');
  const white05 = useTheme('white05');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    handleSheetStyle: {
      backgroundColor: primary,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      paddingVertical: 5,
    },
    sheetContainer: {
      backgroundColor: card,
    },
    contentContainer: {
      padding: 10,
      flex: 1,
      height: '100%',
    },
    errorText: {
      color: 'red',
      marginTop: 6,
      textAlign: 'right',
    },
    assetContainer: {
      gap: 16,
      backgroundColor: white05,
      borderRadius: 16,
      padding: 16,
      borderWidth: 1,
      borderColor: white05,
    },
    error: {
      borderColor: 'red',
    },
  });

  return { styles };
};
