import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { formatUnits } from 'viem';
import { CustomButton } from '@/components/Button';
import { ModalConfirmPinCode } from '@/components/ModalConfirmPinCode';
import { Show } from '@/components/Show';
import { ViewSafeArea } from '@/components/ui/ViewSafeArea';
import { useCreateTxTransferToken } from '@/hooks/useCreateTxTransferToken';
import { useGetSafeBalances } from '@/hooks/useGetSafeBalances';
import { useTheme } from '@/hooks/useThemeColor';
import { toastError, toastSuccess } from '@/utils/toast';
import { ZOD_ERRORS } from '@/utils/zodError';
import { FormSendToken } from './component/FormSendToken';
import { SendTokenConfirm } from './component/SendTokenConfirm';
import { TransferFormData, transferTokenSchema } from './transferTokenSchema';

export const SendToken = () => {
  const { styles } = useStyles();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const { data: safeTokens } = useGetSafeBalances({ limit: 10 });
  const tokens = safeTokens?.pages.flatMap((page) => page.results) || [];
  const { mutateAsync: createTxTransferToken, isPending: isCreatingTx } = useCreateTxTransferToken();

  const methods = useForm<TransferFormData>({
    resolver: zodResolver(transferTokenSchema),
    defaultValues: {
      isConfirm: false,
      transfers: [
        {
          recipientAddress: '',
          token: '',
          amount: '',
        },
      ],
    },
  });

  const { setValue, handleSubmit, setError, watch } = methods;
  const [isConfirm, transfers] = watch(['isConfirm', 'transfers']);

  const currentTransfers =
    transfers?.map((transfer) => ({
      ...transfer,
      tokenInfo: tokens.find((t) => t.tokenAddress === transfer.token),
    })) || [];

  async function handleConfirmSend(data: TransferFormData) {
    try {
      // Validate all transfers
      for (let i = 0; i < data.transfers.length; i++) {
        const transfer = data.transfers[i];
        const currentToken = tokens.find((t) => t.tokenAddress === transfer.token);

        if (!currentToken || !currentToken.token) {
          return setError(`transfers.${i}.token`, { message: ZOD_ERRORS.required });
        }

        const currentTokenBalance = formatUnits(BigInt(currentToken?.balance), currentToken.token.decimals);
        if (Number(transfer.amount) > Number(currentTokenBalance)) {
          return setError(`transfers.${i}.amount`, { message: 'Insufficient balance' }, { shouldFocus: true });
        }
      }

      // sum amount by token address and check if total amount is greater than balance
      const totalAmountByToken = currentTransfers.reduce(
        (acc, transfer) => {
          acc[transfer.token] = (acc[transfer.token] || 0) + Number(transfer.amount);
          return acc;
        },
        {} as Record<string, number>
      );

      for (const tokenAddress in totalAmountByToken) {
        const currentToken = tokens.find((t) => t.tokenAddress === tokenAddress);
        if (!currentToken || !currentToken.token) continue;

        const currentTokenBalance = formatUnits(BigInt(currentToken?.balance), currentToken.token.decimals);
        if (totalAmountByToken[tokenAddress] > Number(currentTokenBalance)) {
          return setError(`transfers.${0}.amount`, { message: 'Insufficient balance' }, { shouldFocus: true });
        }
      }

      if (!isConfirm) {
        setValue('isConfirm', true, { shouldValidate: true });
      } else {
        setIsModalVisible(true);
      }
    } catch (error) {
      toastError(error);
    }
  }

  async function handleBack() {
    setValue('isConfirm', false, { shouldValidate: true });
  }

  const handleClose = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  const handleSubmitCreateTx = useCallback(async () => {
    try {
      await createTxTransferToken({
        transfers: currentTransfers,
      });

      toastSuccess('Transactions created successfully');
      router.back();
    } catch (error) {
      toastError(error);
    }
  }, [createTxTransferToken, currentTransfers]);

  return (
    <FormProvider {...methods}>
      <GestureHandlerRootView>
        <BottomSheetModalProvider>
          <ViewSafeArea edges={['bottom']}>
            <Show when={!isConfirm}>
              <FormSendToken />
            </Show>

            <Show when={isConfirm}>
              <SendTokenConfirm transfers={currentTransfers} />
            </Show>

            <View style={styles.actions}>
              <Show when={isConfirm}>
                <CustomButton type='secondary' onPress={handleBack} disabled={isCreatingTx}>
                  Back
                </CustomButton>
              </Show>

              <CustomButton onPress={handleSubmit(handleConfirmSend)} isLoading={isCreatingTx}>
                {isConfirm ? 'Confirm' : 'Continue'}
              </CustomButton>
            </View>

            <ModalConfirmPinCode isVisible={isModalVisible} onCancel={handleClose} onConfirm={handleSubmitCreateTx} />
          </ViewSafeArea>
        </BottomSheetModalProvider>
      </GestureHandlerRootView>
    </FormProvider>
  );
};

const useStyles = () => {
  const Neutrals800 = useTheme('Neutrals/800');

  const styles = StyleSheet.create({
    actions: {
      padding: 10,
      gap: 8,
      backgroundColor: Neutrals800,
    },
  });

  return { styles };
};
