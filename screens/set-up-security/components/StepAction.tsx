import { StyleSheet, View } from 'react-native';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/hooks/useThemeColor';
import { DoneText } from './DoneText';

type Props = {
  title: string;
  description: string;
  disabled?: boolean;
  hasDone?: boolean;
  note?: string;
};

export const StepAction = ({ title, description, disabled, hasDone = false, note }: Props) => {
  const { styles } = useStyles();

  return (
    <View style={styles.container}>
      <View>
        <View style={styles.rowCenter}>
          <ThemedText type='defaultSemiBold' style={[styles.title, (disabled || hasDone) && styles.textDisabled]}>
            {title}
          </ThemedText>

          <Show when={hasDone}>
            <Spacer width={4} />

            <DoneText />
          </Show>
        </View>

        <ThemedText type='smallLight' style={[styles.description, (disabled || hasDone) && styles.textDisabled]}>
          {description}
        </ThemedText>
      </View>

      <Show when={!hasDone && !!note}>
        <DoneText text={note} />
      </Show>
    </View>
  );
};

const useStyles = () => {
  const white50 = useTheme('white50');
  const white04 = useTheme('white04');
  const Text_Neutral_Tertiary = useTheme('Text/Neutral/Tertiary');

  const styles = StyleSheet.create({
    rowCenter: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    textDisabled: {
      color: Text_Neutral_Tertiary,
    },
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      backgroundColor: white04,
      borderRadius: 16,
      paddingHorizontal: 16,
      paddingVertical: 10,
    },
    title: {
      color: '#fff',
    },
    description: {
      color: white50,
    },
  });

  return { styles };
};
