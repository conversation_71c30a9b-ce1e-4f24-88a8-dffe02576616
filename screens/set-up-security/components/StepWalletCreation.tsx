import { StyleSheet, View } from 'react-native';
import { StepProgress } from '@/components/StepProgress';
import { ThemedText } from '@/components/ThemedText';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { StepAction } from './StepAction';

type Props = {};

export const StepWalletCreation = (props: Props) => {
  const { styles } = useStyles();
  const { data: recoveryKeys = [] } = useGetRecoveryKeysNewWallet();
  const isStepActive = recoveryKeys?.length > 0;

  return (
    <View style={styles.step}>
      <View style={styles.stepBox}>
        <StepProgress isActive={isStepActive} />
      </View>

      <View style={styles.container}>
        <ThemedText type='tinyMedium' style={[isStepActive && styles.stepActive]}>
          Step 2
        </ThemedText>

        <ThemedText type='defaultSemiBold'>Wallet setup</ThemedText>

        <StepAction hasDone={false} title='Create Wallet' description='Set up multi sig wallet' />

        <StepAction hasDone={false} title='Secure wallet' description='Complete wallet request' />
      </View>
    </View>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    stepBox: {
      flexDirection: 'row',
      paddingLeft: 16,
      paddingRight: 12,
      paddingTop: 4,
    },
    step: {
      flexDirection: 'row',
      alignItems: 'stretch',
    },
    stepActive: {
      color: primary,
    },
    container: {
      gap: 8,
      flex: 1,
    },
  });

  return { styles };
};
