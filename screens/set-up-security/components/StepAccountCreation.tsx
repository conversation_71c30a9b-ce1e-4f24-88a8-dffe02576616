import { StyleSheet, View } from 'react-native';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { StepProgress } from '@/components/StepProgress';
import { ThemedText } from '@/components/ThemedText';
import { useGetBiometric } from '@/hooks/useGetBiometric';
import { useGetPin } from '@/hooks/useGetPin';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { DoneText } from './DoneText';
import { StepAction } from './StepAction';

type Props = {};

export const StepAccountCreation = (props: Props) => {
  const { styles } = useStyles();
  const { data: hashingPassCode } = useGetPin();
  const { data: biometricEnabled } = useGetBiometric();
  const { data: recoveryKeys } = useGetRecoveryKeysNewWallet();

  const isComplete = !!hashingPassCode && biometricEnabled && (recoveryKeys?.length || 0) > 0;
  const isStepActive = !isComplete;

  return (
    <View style={styles.step}>
      <View style={styles.stepBox}>
        <StepProgress isActive={isStepActive} />
      </View>

      <View style={styles.container}>
        <ThemedText type={isComplete ? 'tinyLight' : 'tinyMedium'} style={[isStepActive && styles.stepActive]}>
          {isComplete ? 'Complete' : 'Step 1'}
        </ThemedText>

        <View style={styles.titleBox}>
          <ThemedText type='defaultSemiBold'>Account creation</ThemedText>

          <Show when={isComplete}>
            <DoneText />
          </Show>
        </View>

        <StepAction hasDone={!!hashingPassCode} title='Set up PIN' description='Create a custom PIN' />

        <StepAction
          hasDone={biometricEnabled}
          title='Biometric'
          description='Setup secure access'
          note='Highly recommended'
        />

        <StepAction
          hasDone={Number(recoveryKeys?.length || 0) > 0}
          title='Secure recovery'
          description='Securely store off device'
        />

        <Spacer height={16} />
      </View>
    </View>
  );
};

const useStyles = () => {
  const primary = useTheme('primary');

  const styles = StyleSheet.create({
    titleBox: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    stepBox: {
      flexDirection: 'row',
      height: '100%',
      paddingLeft: 16,
      paddingRight: 12,
      paddingTop: 4,
    },
    step: {
      flexDirection: 'row',
      alignItems: 'stretch',
    },
    stepActive: {
      color: primary,
    },
    container: {
      gap: 8,
      flex: 1,
    },
  });

  return { styles };
};
