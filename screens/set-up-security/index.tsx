import { Header, HeaderOptions } from '@react-navigation/elements';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { router, useNavigation } from 'expo-router';
import { useEffect } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CustomButton } from '@/components/Button';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useGetBiometric } from '@/hooks/useGetBiometric';
import { useGetPin } from '@/hooks/useGetPin';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useTheme } from '@/hooks/useThemeColor';
import { StepAccountCreation } from './components/StepAccountCreation';
import { StepWalletCreation } from './components/StepWalletCreation';

type Props = {};

export const SetUpSecurity = (props: Props) => {
  const { styles } = useStyles();
  const navigation = useNavigation();

  const { data: hashingPassCode, isPending: isFetchingPin } = useGetPin();
  const { data: biometricEnabled, isPending: isFetchingBiometric } = useGetBiometric();
  const { data: recoveryKeys, isPending: isFetchingRecoveryKeys } = useGetRecoveryKeysNewWallet();

  const isStep1Complete = !!hashingPassCode && biometricEnabled && !!recoveryKeys;
  const isPending = isFetchingPin || isFetchingBiometric || isFetchingRecoveryKeys;

  const handleConfirm = async () => {
    if (!hashingPassCode) {
      return router.navigate('/(new-wallet)/set-up-pin');
    }

    if (recoveryKeys?.length && recoveryKeys?.length > 0) {
      return router.navigate('/(new-wallet)/set-up-wallet');
    }

    if (!biometricEnabled) {
      return router.navigate('/(new-wallet)/set-up-biometric');
    }

    let settings = await Notifications.getPermissionsAsync();
    if (!settings.granted) {
      return router.navigate('/(new-wallet)/set-up-notification');
    }

    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== Location.PermissionStatus.GRANTED) {
      return router.navigate('/(new-wallet)/set-up-location');
    }

    if (!recoveryKeys?.length) {
      return router.navigate('/(new-wallet)/set-up-recovery-key');
    }

    router.navigate('/(new-wallet)/set-up-wallet');
  };

  useEffect(() => {
    navigation.setOptions({
      header: (options: HeaderOptions) => (
        <Header
          {...options}
          headerTransparent
          title=''
          {...{
            headerTintColor: '#fff',
            headerBackButtonDisplayMode: 'minimal',
            headerBackButtonMenuEnabled: false,
          }}
        />
      ),
    });
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView bounces={false} style={styles.pH} showsVerticalScrollIndicator={false}>
        <Show when={isStep1Complete}>
          <ThemedText type='tinyLight' style={styles.nextStepTitle}>
            Next Step
          </ThemedText>
        </Show>

        <Spacer height={8} />

        <ThemedText type='title' style={styles.title}>
          {isStep1Complete ? 'Set up wallet' : 'Set Up Security'}
        </ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallLight' style={styles.description}>
          {isStep1Complete
            ? 'Start by creating a highly secure Multi Sig wallet to manage your funds and prevent unauthorized payments'
            : 'Take the first step to protect your wallet. Secure you wallet on this device so only you can access'}
        </ThemedText>

        <Spacer height={32} />

        <StepAccountCreation />

        <StepWalletCreation />
      </ScrollView>

      <Spacer height={16} />

      <View style={styles.pH}>
        <CustomButton type='primary' onPress={handleConfirm} disabled={isPending}>
          {isStep1Complete ? 'Setup wallet' : 'Get started'}
        </CustomButton>
      </View>
    </SafeAreaView>
  );
};

const useStyles = () => {
  const backgroundColor = useTheme('background');
  const white90 = useTheme('white90');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
      paddingVertical: 16,
    },
    title: {
      // color: primary,
      textAlign: 'center',
    },
    description: {
      color: white90,
      textAlign: 'center',
    },
    nextStepTitle: {
      color: '#B7B7C7',
      textAlign: 'center',
    },
    pH: {
      paddingHorizontal: 16,
    },
  });

  return { styles };
};
