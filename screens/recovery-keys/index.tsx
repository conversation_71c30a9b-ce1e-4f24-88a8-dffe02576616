import { useDebouncedValue } from '@mantine/hooks';
import { useQueryClient } from '@tanstack/react-query';
import * as Clipboard from 'expo-clipboard';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { FlatList, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { isAddress } from 'viem';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { useGetRecoveryKeys } from '@/hooks/useGetRecoveryKeys';
import { useTheme } from '@/hooks/useThemeColor';
import { queryKeys } from '@/utils/queryKeys';

type Props = {};

export const RecoveryKeys = (props: Props) => {
  const { styles, colors } = useStyles();

  const [safeWalletAddress, setSafeWalletAddress] = useState('');

  const queryClient = useQueryClient();
  const [safeWalletAddressDebounced] = useDebouncedValue(safeWalletAddress, 200);

  const {
    data: recoveryKeys = [],
    refetch: refetchRecoveryKeys,
    isFetching: isFetchingRecoveryKeys,
  } = useGetRecoveryKeys(
    { safeAddress: safeWalletAddressDebounced },
    {
      enabled: isAddress(safeWalletAddressDebounced),
    }
  );

  const handlePaste = async () => {
    const content = await Clipboard.getStringAsync();
    setSafeWalletAddress(content);
  };

  const handleDirectRecoveryWallet = (recoveryKey: string) => {
    router.navigate({
      pathname: '/(recovery-wallet)/recovery-wallet',
      params: {
        safeAddress: safeWalletAddress,
        recoveryKey,
      },
    });
  };

  useEffect(() => {
    if (!isAddress(safeWalletAddress)) {
      queryClient.removeQueries({ queryKey: queryKeys.recoveryKeys });
    }
  }, [safeWalletAddress, queryClient.removeQueries]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ThemedText type='title'>Recovery keys</ThemedText>

      <Spacer height={16} />

      <ThemedText type='smallLight'>Select your recovery keys to restore your wallet</ThemedText>

      <Spacer height={16} />

      <TextInput
        label='GeoSafe Wallet Address'
        placeholder='Enter GeoSafe Wallet Address'
        value={safeWalletAddress}
        onChangeText={setSafeWalletAddress}
        rightIcon={<Icons.Paste size={24} color={colors.white65} />}
        onRightIconPress={handlePaste}
      />

      <Spacer height={16} />

      <ThemedText type='smallLight'>List recovery keys</ThemedText>

      <Spacer height={16} />

      <FlatList
        bounces={true}
        // refreshing={isFetchingRecoveryKeys}
        // onRefresh={refetchRecoveryKeys}
        refreshControl={
          <RefreshControl
            refreshing={isFetchingRecoveryKeys}
            onRefresh={refetchRecoveryKeys}
            tintColor={colors.white65}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ gap: 16 }}
        keyboardShouldPersistTaps='handled'
        data={safeWalletAddressDebounced ? recoveryKeys : []}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.containerKey}
            key={item}
            onPress={() => handleDirectRecoveryWallet(item?.substring(2))}
          >
            <View style={styles.boxKey}>
              <ThemedText>{item?.substring(2)}</ThemedText>
            </View>
          </TouchableOpacity>
        )}
      />
    </SafeAreaView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');
  const white65 = useTheme('white65');

  const styles = StyleSheet.create({
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
    },
    container: {
      flex: 1,
      padding: 10,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
  });

  return { styles, colors: { white65 } };
};
