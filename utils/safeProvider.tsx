import Safe from '@safe-global/protocol-kit';
import { PropsWithChildren, useEffect, useState } from 'react';
import { SafeContextProvider } from '@/context/safeContext';
import { useGetWallet } from '@/hooks/useGetWallet';
import { useWalletStore } from '@/store/wallet';
import { env } from './env';
import { toastError } from './toast';

type Props = {};

export const SafeProvider = ({ children }: PropsWithChildren<Props>) => {
  const [safeInstance, setSafeInstance] = useState<Safe | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const safeWallet = useWalletStore.use.safeWallet();
  const { data: signer, isPending } = useGetWallet();

  useEffect(() => {
    if (!signer || !safeWallet) return;

    (async () => {
      const SIGNER_PRIVATE_KEY = `0x${signer.privateKey}` as const;

      try {
        setIsLoading(true);
        const protocolKitInstance = await Safe.init({
          safeAddress: safeWallet,
          provider: env.RPC_URL,
          signer: SIGNER_PRIVATE_KEY,
        });

        setSafeInstance(protocolKitInstance);
      } catch (error) {
        toastError(error);
      } finally {
        setIsLoading(false);
      }
    })();
  }, [safeWallet, signer]);

  if (isPending) return null;

  return (
    <SafeContextProvider
      value={{
        safeInstance,
        isLoading,
        signer,
        safeWallet,
      }}
    >
      {children}
    </SafeContextProvider>
  );
};
