import '@walletconnect/react-native-compat';

import { AppKitOptions, createAppKit, defaultWagmiConfig } from '@reown/appkit-wagmi-react-native';
import { sepolia } from '@wagmi/core/chains';
import { WagmiProvider } from 'wagmi';
import { env } from './env';

// 1. Get projectId
const projectId = env.WALLET_CONNECT_PROJECT_ID;

// 2. Create config
const metadata: AppKitOptions['metadata'] = {
  name: 'GeoSafe',
  description: 'GeoSafe Wallet',
  url: 'https://reown.com/appkit',
  icons: ['https://avatars.githubusercontent.com/u/179229932'],
  redirect: {
    native: 'geosafe://',
  },
};

const chains = [sepolia] as const;

const wagmiConfig = defaultWagmiConfig({
  chains,
  projectId,
  metadata,
});

// 3. Create modal
createAppKit({
  projectId,
  metadata,
  wagmiConfig,
  defaultChain: sepolia,
  enableAnalytics: true,
  // connectorImages: {
  //   coinbaseWallet:
  //     'https://play-lh.googleusercontent.com/wrgUujbq5kbn4Wd4tzyhQnxOXkjiGqq39N4zBvCHmxpIiKcZw_Pb065KTWWlnoejsg',
  //   appKitAuth: 'https://avatars.githubusercontent.com/u/179229932',
  // },
});

export const WagmiProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  return <WagmiProvider config={wagmiConfig}>{children}</WagmiProvider>;
};
