import SafeApiKit from '@safe-global/api-kit';
import { ExternalSigner } from '@safe-global/protocol-kit';
import { Address, createPublicClient, createWalletClient, Hex, http, PrivateKeyAccount } from 'viem';
import { privateKeyToAccount } from 'viem/accounts';
import { sepolia } from 'viem/chains';
import { env } from './env';

export const CURRENT_CHAIN = sepolia;

export const pkToAccount = (privateKey: Hex) => {
  const signer = privateKeyToAccount(privateKey);
  return signer;
};

export const getWalletClient = (account?: PrivateKeyAccount) =>
  createWalletClient({
    account,
    chain: CURRENT_CHAIN,
    transport: http(env.RPC_URL),
  });

export const getPublicClient = () =>
  createPublicClient({
    chain: CURRENT_CHAIN,
    transport: http(env.RPC_URL),
  });

export const bigIntReplacer = (_key: string, value: any) => (typeof value === 'bigint' ? value.toString() : value);

export const getAuthorization = async (walletClient: ExternalSigner, contractAddress: Address) => {
  const authorization = await walletClient.signAuthorization({
    account: walletClient.account,
    contractAddress,
  });

  const serializedAuth = JSON.stringify(authorization, bigIntReplacer);
  return serializedAuth;
};

export function encodeKey(publicKey: string, deviceId: string): string {
  const timestamp = Date.now().toString();
  const payload = `${publicKey}:${deviceId}:${timestamp}`;
  return Buffer.from(payload, 'utf8').toString('hex');
}

export const apiKit = new SafeApiKit({
  chainId: BigInt(CURRENT_CHAIN.id),
  apiKey: env.SAFE_API_KEY,
});
